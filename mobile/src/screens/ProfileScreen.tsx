import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { Card, Input, Button } from '../components';
import { useAuth } from '../context/AuthContext';
import { getInitials, calculateAge } from '../utils/helpers';
import ApiService from '../services/api';
import { AppResetManager } from '../utils/appReset';

export const ProfileScreen: React.FC = () => {
  const navigation = useNavigation();
  const { selectedPatient, user, logout, selectPatient, patients } = useAuth();
  
  const [isEditing, setIsEditing] = useState(false);
  const [isUpdating, setIsUpdating] = useState(false);
  const [formData, setFormData] = useState({
    email: selectedPatient?.contact_info?.email || '',
    mobile: selectedPatient?.contact_info?.mobile || '',
    emergency_contact: selectedPatient?.contact_info?.emergency_contact || '',
    street: selectedPatient?.address?.street || '',
    city: selectedPatient?.address?.city || '',
    state: selectedPatient?.address?.state || '',
    postal_code: selectedPatient?.address?.postal_code || '',
    country: selectedPatient?.address?.country || '',
  });

  const handleEdit = () => {
    setIsEditing(true);
  };

  const handleCancel = () => {
    setIsEditing(false);
    // Reset form data
    setFormData({
      email: selectedPatient?.contact_info?.email || '',
      mobile: selectedPatient?.contact_info?.mobile || '',
      emergency_contact: selectedPatient?.contact_info?.emergency_contact || '',
      street: selectedPatient?.address?.street || '',
      city: selectedPatient?.address?.city || '',
      state: selectedPatient?.address?.state || '',
      postal_code: selectedPatient?.address?.postal_code || '',
      country: selectedPatient?.address?.country || '',
    });
  };

  const handleSave = async () => {
    if (!selectedPatient) return;

    setIsUpdating(true);
    try {
      const updates = {
        contact_info: {
          email: formData.email,
          mobile: formData.mobile,
          emergency_contact: formData.emergency_contact,
        },
        address: {
          street: formData.street,
          city: formData.city,
          state: formData.state,
          postal_code: formData.postal_code,
          country: formData.country,
        },
      };

      const response = await ApiService.updateDemographics(selectedPatient.id, updates);
      
      if (response.success && response.data) {
        // Update the selected patient with new data
        selectPatient(response.data);
        setIsEditing(false);
        Alert.alert('Success', 'Profile updated successfully!');
      } else {
        Alert.alert('Error', response.message || 'Failed to update profile');
      }
    } catch (error) {
      Alert.alert('Error', 'Something went wrong. Please try again.');
    } finally {
      setIsUpdating(false);
    }
  };

  const handleChangePatient = () => {
    navigation.navigate('SelectPatient' as never);
  };

  const handleLogout = () => {
    Alert.alert(
      'Logout',
      'Are you sure you want to logout?',
      [
        { text: 'Cancel', style: 'cancel' },
        { text: 'Logout', style: 'destructive', onPress: logout },
      ]
    );
  };

  const handleResetApp = () => {
    if (__DEV__) {
      Alert.alert(
        'Reset App',
        'This will clear all app data and restart fresh. This is only available in development mode.',
        [
          { text: 'Cancel', style: 'cancel' },
          {
            text: 'Reset',
            style: 'destructive',
            onPress: async () => {
              try {
                await AppResetManager.forceReset();
                Alert.alert('App Reset', 'App has been reset. Please reload the app.');
              } catch (error) {
                Alert.alert('Error', 'Failed to reset app');
              }
            }
          },
        ]
      );
    }
  };

  if (!selectedPatient) {
    return (
      <View style={styles.container}>
        <Text style={styles.noPatientText}>No patient selected</Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>Profile</Text>
        {!isEditing && (
          <TouchableOpacity onPress={handleEdit}>
            <Text style={styles.editButton}>Edit</Text>
          </TouchableOpacity>
        )}
      </View>

      <ScrollView style={styles.content}>
        {/* Patient Info */}
        <Card style={styles.section}>
          <View style={styles.patientHeader}>
            <View style={styles.avatar}>
              <Text style={styles.avatarText}>{getInitials(selectedPatient.name)}</Text>
            </View>
            <View style={styles.patientInfo}>
              <Text style={styles.patientName}>{selectedPatient.name}</Text>
              <Text style={styles.patientMeta}>
                {calculateAge(selectedPatient.date_of_birth)} years • {selectedPatient.gender}
              </Text>
              <Text style={styles.patientId}>ID: {selectedPatient.id.slice(0, 8)}</Text>
            </View>
          </View>
        </Card>

        {/* Contact Information */}
        <Card style={styles.section}>
          <Text style={styles.sectionTitle}>Contact Information</Text>
          
          <Input
            label="Email"
            value={formData.email}
            onChangeText={(value) => setFormData(prev => ({ ...prev, email: value }))}
            placeholder="<EMAIL>"
            keyboardType="email-address"
            editable={isEditing}
            containerStyle={styles.inputContainer}
          />

          <Input
            label="Mobile Number"
            value={formData.mobile}
            onChangeText={(value) => setFormData(prev => ({ ...prev, mobile: value }))}
            placeholder="Mobile number"
            keyboardType="phone-pad"
            editable={isEditing}
            containerStyle={styles.inputContainer}
          />

          <Input
            label="Emergency Contact"
            value={formData.emergency_contact}
            onChangeText={(value) => setFormData(prev => ({ ...prev, emergency_contact: value }))}
            placeholder="Emergency contact number"
            keyboardType="phone-pad"
            editable={isEditing}
            containerStyle={styles.inputContainer}
          />
        </Card>

        {/* Address */}
        <Card style={styles.section}>
          <Text style={styles.sectionTitle}>Address</Text>
          
          <Input
            label="Street Address"
            value={formData.street}
            onChangeText={(value) => setFormData(prev => ({ ...prev, street: value }))}
            placeholder="Street address"
            editable={isEditing}
            containerStyle={styles.inputContainer}
          />

          <View style={styles.row}>
            <Input
              label="City"
              value={formData.city}
              onChangeText={(value) => setFormData(prev => ({ ...prev, city: value }))}
              placeholder="City"
              editable={isEditing}
              containerStyle={[styles.inputContainer, styles.halfWidth]}
            />

            <Input
              label="State"
              value={formData.state}
              onChangeText={(value) => setFormData(prev => ({ ...prev, state: value }))}
              placeholder="State"
              editable={isEditing}
              containerStyle={[styles.inputContainer, styles.halfWidth, styles.marginLeft]}
            />
          </View>

          <View style={styles.row}>
            <Input
              label="Postal Code"
              value={formData.postal_code}
              onChangeText={(value) => setFormData(prev => ({ ...prev, postal_code: value }))}
              placeholder="Postal code"
              editable={isEditing}
              containerStyle={[styles.inputContainer, styles.halfWidth]}
            />

            <Input
              label="Country"
              value={formData.country}
              onChangeText={(value) => setFormData(prev => ({ ...prev, country: value }))}
              placeholder="Country"
              editable={isEditing}
              containerStyle={[styles.inputContainer, styles.halfWidth, styles.marginLeft]}
            />
          </View>
        </Card>

        {/* Account Actions */}
        <Card style={styles.section}>
          <Text style={styles.sectionTitle}>Account</Text>
          
          <TouchableOpacity style={styles.actionRow} onPress={handleChangePatient}>
            <Text style={styles.actionText}>Switch Patient</Text>
            <Text style={styles.actionArrow}>→</Text>
          </TouchableOpacity>

          <TouchableOpacity style={styles.actionRow}>
            <Text style={styles.actionText}>Privacy Policy</Text>
            <Text style={styles.actionArrow}>→</Text>
          </TouchableOpacity>

          <TouchableOpacity style={styles.actionRow}>
            <Text style={styles.actionText}>Terms of Service</Text>
            <Text style={styles.actionArrow}>→</Text>
          </TouchableOpacity>

          {__DEV__ && (
            <TouchableOpacity style={styles.actionRow} onPress={handleResetApp}>
              <Text style={[styles.actionText, { color: '#f59e0b' }]}>Reset App (Dev)</Text>
              <Text style={styles.actionArrow}>⚠️</Text>
            </TouchableOpacity>
          )}

          <TouchableOpacity style={[styles.actionRow, styles.logoutRow]} onPress={handleLogout}>
            <Text style={styles.logoutText}>Logout</Text>
          </TouchableOpacity>
        </Card>

        {isEditing && (
          <View style={styles.editActions}>
            <Button
              title="Cancel"
              onPress={handleCancel}
              variant="outline"
              style={styles.cancelButton}
            />
            <Button
              title={isUpdating ? 'Saving...' : 'Save Changes'}
              onPress={handleSave}
              loading={isUpdating}
              style={styles.saveButton}
            />
          </View>
        )}
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f9fafb',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 24,
    paddingTop: 60,
    backgroundColor: '#ffffff',
    borderBottomWidth: 1,
    borderBottomColor: '#e5e7eb',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#111827',
  },
  editButton: {
    fontSize: 16,
    color: '#2563eb',
    fontWeight: '500',
  },
  content: {
    flex: 1,
    padding: 24,
  },
  section: {
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#111827',
    marginBottom: 16,
  },
  patientHeader: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  avatar: {
    width: 60,
    height: 60,
    borderRadius: 30,
    backgroundColor: '#2563eb',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  avatarText: {
    color: '#ffffff',
    fontSize: 20,
    fontWeight: 'bold',
  },
  patientInfo: {
    flex: 1,
  },
  patientName: {
    fontSize: 20,
    fontWeight: '600',
    color: '#111827',
    marginBottom: 4,
  },
  patientMeta: {
    fontSize: 14,
    color: '#6b7280',
    marginBottom: 2,
  },
  patientId: {
    fontSize: 12,
    color: '#9ca3af',
  },
  inputContainer: {
    marginBottom: 16,
  },
  row: {
    flexDirection: 'row',
  },
  halfWidth: {
    flex: 1,
  },
  marginLeft: {
    marginLeft: 12,
  },
  actionRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#f3f4f6',
  },
  actionText: {
    fontSize: 16,
    color: '#111827',
  },
  actionArrow: {
    fontSize: 16,
    color: '#9ca3af',
  },
  logoutRow: {
    borderBottomWidth: 0,
  },
  logoutText: {
    fontSize: 16,
    color: '#ef4444',
    fontWeight: '500',
  },
  editActions: {
    flexDirection: 'row',
    gap: 12,
    marginTop: 16,
    marginBottom: 32,
  },
  cancelButton: {
    flex: 1,
  },
  saveButton: {
    flex: 1,
  },
  noPatientText: {
    fontSize: 18,
    color: '#6b7280',
    textAlign: 'center',
    marginTop: 100,
  },
});
