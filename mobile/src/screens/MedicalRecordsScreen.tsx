import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  RefreshControl,
  Alert,
} from 'react-native';
import { Card, Loading } from '../components';
import { useAuth } from '../context/AuthContext';
import { MedicalRecord } from '../types';
import { formatDate } from '../utils/helpers';
import ApiService from '../services/api';

const RECORD_ICONS: { [key: string]: string } = {
  'pathology': '🧪',
  'investigation': '🔬',
  'vitals': '❤️',
  'admission': '🏥',
  'discharge': '🚪',
  'medication': '💊',
  'emr': '📋',
};

const RECORD_COLORS: { [key: string]: string } = {
  'pathology': '#ef4444',
  'investigation': '#3b82f6',
  'vitals': '#10b981',
  'admission': '#f59e0b',
  'discharge': '#6b7280',
  'medication': '#8b5cf6',
  'emr': '#06b6d4',
};

export const MedicalRecordsScreen: React.FC = () => {
  const { selectedPatient } = useAuth();
  
  const [records, setRecords] = useState<MedicalRecord[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [selectedType, setSelectedType] = useState<string | null>(null);

  useEffect(() => {
    if (selectedPatient) {
      loadRecords();
    }
  }, [selectedPatient]);

  const loadRecords = async () => {
    if (!selectedPatient) return;

    try {
      console.log('Loading medical records for patient:', selectedPatient.id);
      const response = await ApiService.getMedicalRecords(selectedPatient.id);
      console.log('Medical records response:', response);

      if (response.success && response.data) {
        setRecords(response.data);
        console.log('Loaded', response.data.length, 'medical records');
      } else {
        console.warn('No medical records found or API error:', response.message);
        setRecords([]);
      }
    } catch (error) {
      console.error('Error loading medical records:', error);
      setRecords([]);
      // Show error to user only if not refreshing (to avoid spam)
      if (!refreshing) {
        Alert.alert(
          'Connection Error',
          'Unable to load medical records. Please check your internet connection and try again.'
        );
      }
    } finally {
      setIsLoading(false);
      setRefreshing(false);
    }
  };

  const onRefresh = () => {
    setRefreshing(true);
    loadRecords();
  };

  const getFilteredRecords = () => {
    if (!selectedType) return records;
    return records.filter(record => record.type === selectedType);
  };

  const getRecordTypes = () => {
    const types = [...new Set(records.map(record => record.type))];
    return types;
  };

  const renderTypeFilter = () => {
    const types = getRecordTypes();
    
    return (
      <View style={styles.filterContainer}>
        <TouchableOpacity
          style={[
            styles.filterChip,
            !selectedType && styles.filterChipActive,
          ]}
          onPress={() => setSelectedType(null)}
        >
          <Text
            style={[
              styles.filterChipText,
              !selectedType && styles.filterChipTextActive,
            ]}
          >
            All
          </Text>
        </TouchableOpacity>
        
        {types.map((type) => (
          <TouchableOpacity
            key={type}
            style={[
              styles.filterChip,
              selectedType === type && styles.filterChipActive,
            ]}
            onPress={() => setSelectedType(type)}
          >
            <Text
              style={[
                styles.filterChipText,
                selectedType === type && styles.filterChipTextActive,
              ]}
            >
              {RECORD_ICONS[type]} {type.charAt(0).toUpperCase() + type.slice(1)}
            </Text>
          </TouchableOpacity>
        ))}
      </View>
    );
  };

  const renderRecordCard = ({ item }: { item: MedicalRecord }) => (
    <Card style={styles.recordCard}>
      <View style={styles.recordHeader}>
        <View style={styles.recordTitleContainer}>
          <View
            style={[
              styles.recordTypeIndicator,
              { backgroundColor: RECORD_COLORS[item.type] || '#6b7280' },
            ]}
          >
            <Text style={styles.recordTypeIcon}>
              {RECORD_ICONS[item.type] || '📄'}
            </Text>
          </View>
          <View style={styles.recordTitleInfo}>
            <Text style={styles.recordTitle}>{item.title}</Text>
            <Text style={styles.recordType}>
              {item.type.charAt(0).toUpperCase() + item.type.slice(1)}
            </Text>
          </View>
        </View>
        <Text style={styles.recordDate}>{formatDate(item.date)}</Text>
      </View>

      {item.description && (
        <Text style={styles.recordDescription} numberOfLines={3}>
          {item.description}
        </Text>
      )}

      {item.doctor_name && (
        <Text style={styles.doctorName}>Dr. {item.doctor_name}</Text>
      )}

      {/* Display some data if available */}
      {item.data && typeof item.data === 'object' && (
        <View style={styles.dataContainer}>
          {Object.entries(item.data).slice(0, 3).map(([key, value]) => (
            <View key={key} style={styles.dataRow}>
              <Text style={styles.dataKey}>{key}:</Text>
              <Text style={styles.dataValue}>{String(value)}</Text>
            </View>
          ))}
        </View>
      )}
    </Card>
  );

  if (!selectedPatient) {
    return (
      <View style={styles.container}>
        <Text style={styles.noPatientText}>No patient selected</Text>
      </View>
    );
  }

  if (isLoading) {
    return <Loading message="Loading medical records..." />;
  }

  const filteredRecords = getFilteredRecords();

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>Medical Records</Text>
        <Text style={styles.subtitle}>Last 6 months</Text>
      </View>

      {records.length > 0 && renderTypeFilter()}

      {filteredRecords.length > 0 ? (
        <FlatList
          data={filteredRecords}
          renderItem={renderRecordCard}
          keyExtractor={(item) => item.id}
          refreshControl={
            <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
          }
          contentContainerStyle={styles.listContainer}
          showsVerticalScrollIndicator={false}
        />
      ) : (
        <View style={styles.emptyState}>
          <Text style={styles.emptyIcon}>📋</Text>
          <Text style={styles.emptyTitle}>
            {selectedType ? `No ${selectedType} records` : 'No Medical Records'}
          </Text>
          <Text style={styles.emptyDescription}>
            {selectedType
              ? `No ${selectedType} records found for the last 6 months.`
              : 'No medical records found for the last 6 months.'}
          </Text>
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f9fafb',
  },
  header: {
    padding: 24,
    paddingTop: 60,
    backgroundColor: '#ffffff',
    borderBottomWidth: 1,
    borderBottomColor: '#e5e7eb',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#111827',
  },
  subtitle: {
    fontSize: 14,
    color: '#6b7280',
    marginTop: 4,
  },
  filterContainer: {
    flexDirection: 'row',
    paddingHorizontal: 24,
    paddingVertical: 16,
    backgroundColor: '#ffffff',
  },
  filterChip: {
    backgroundColor: '#f3f4f6',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
    marginRight: 8,
  },
  filterChipActive: {
    backgroundColor: '#2563eb',
  },
  filterChipText: {
    fontSize: 12,
    fontWeight: '500',
    color: '#6b7280',
  },
  filterChipTextActive: {
    color: '#ffffff',
  },
  listContainer: {
    padding: 24,
    paddingBottom: 100,
  },
  recordCard: {
    marginBottom: 16,
  },
  recordHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 12,
  },
  recordTitleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  recordTypeIndicator: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  recordTypeIcon: {
    fontSize: 18,
  },
  recordTitleInfo: {
    flex: 1,
  },
  recordTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#111827',
    marginBottom: 2,
  },
  recordType: {
    fontSize: 12,
    color: '#6b7280',
    textTransform: 'capitalize',
  },
  recordDate: {
    fontSize: 12,
    color: '#9ca3af',
  },
  recordDescription: {
    fontSize: 14,
    color: '#374151',
    lineHeight: 20,
    marginBottom: 8,
  },
  doctorName: {
    fontSize: 12,
    color: '#2563eb',
    fontWeight: '500',
    marginBottom: 8,
  },
  dataContainer: {
    backgroundColor: '#f9fafb',
    padding: 12,
    borderRadius: 8,
  },
  dataRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 4,
  },
  dataKey: {
    fontSize: 12,
    color: '#6b7280',
    fontWeight: '500',
  },
  dataValue: {
    fontSize: 12,
    color: '#111827',
  },
  emptyState: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 32,
  },
  emptyIcon: {
    fontSize: 64,
    marginBottom: 16,
  },
  emptyTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: '#111827',
    marginBottom: 8,
    textAlign: 'center',
  },
  emptyDescription: {
    fontSize: 16,
    color: '#6b7280',
    textAlign: 'center',
    lineHeight: 24,
  },
  noPatientText: {
    fontSize: 18,
    color: '#6b7280',
    textAlign: 'center',
    marginTop: 100,
  },
});
