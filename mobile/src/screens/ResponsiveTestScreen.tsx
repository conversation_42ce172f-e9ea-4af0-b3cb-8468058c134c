import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
} from 'react-native';
import { Card, ResponsiveTimeSlots } from '../components';
import { useResponsive } from '../hooks/useResponsive';

// Mock data for testing
const mockTimeSlots = [
  { id: '1', doctor_id: '1', date: '2025-06-09', start_time: '09:00:00', end_time: '09:30:00', is_available: true },
  { id: '2', doctor_id: '1', date: '2025-06-09', start_time: '09:30:00', end_time: '10:00:00', is_available: true },
  { id: '3', doctor_id: '1', date: '2025-06-09', start_time: '10:00:00', end_time: '10:30:00', is_available: true },
  { id: '4', doctor_id: '1', date: '2025-06-09', start_time: '10:30:00', end_time: '11:00:00', is_available: true },
  { id: '5', doctor_id: '1', date: '2025-06-09', start_time: '11:00:00', end_time: '11:30:00', is_available: true },
  { id: '6', doctor_id: '1', date: '2025-06-09', start_time: '14:00:00', end_time: '14:30:00', is_available: true },
  { id: '7', doctor_id: '1', date: '2025-06-09', start_time: '14:30:00', end_time: '15:00:00', is_available: true },
  { id: '8', doctor_id: '1', date: '2025-06-09', start_time: '15:00:00', end_time: '15:30:00', is_available: true },
];

export const ResponsiveTestScreen: React.FC = () => {
  const responsive = useResponsive();
  const [selectedSlot, setSelectedSlot] = React.useState(null);

  const {
    width,
    height,
    isSmallScreen,
    isMediumScreen,
    isLargeScreen,
    isTablet,
    isPhone,
    orientation,
    scale,
  } = responsive;

  const dynamicStyles = {
    container: {
      padding: isTablet ? 32 : 24,
      maxWidth: isTablet ? 1200 : '100%',
      alignSelf: 'center' as const,
      width: '100%',
    },
  };

  return (
    <ScrollView style={styles.container}>
      <View style={[styles.content, dynamicStyles.container]}>
        <Text style={[styles.title, { fontSize: isTablet ? 28 : 24 }]}>
          Responsive Design Test
        </Text>

        {/* Device Info Card */}
        <Card style={styles.infoCard}>
          <Text style={styles.cardTitle}>Device Information</Text>
          <View style={styles.infoGrid}>
            <View style={styles.infoItem}>
              <Text style={styles.infoLabel}>Screen Size:</Text>
              <Text style={styles.infoValue}>{width} × {height}</Text>
            </View>
            <View style={styles.infoItem}>
              <Text style={styles.infoLabel}>Device Type:</Text>
              <Text style={styles.infoValue}>{isTablet ? 'Tablet' : 'Phone'}</Text>
            </View>
            <View style={styles.infoItem}>
              <Text style={styles.infoLabel}>Orientation:</Text>
              <Text style={styles.infoValue}>{orientation}</Text>
            </View>
            <View style={styles.infoItem}>
              <Text style={styles.infoLabel}>Scale Factor:</Text>
              <Text style={styles.infoValue}>{scale.toFixed(2)}</Text>
            </View>
          </View>
        </Card>

        {/* Breakpoint Status */}
        <Card style={styles.breakpointCard}>
          <Text style={styles.cardTitle}>Breakpoint Status</Text>
          <View style={styles.breakpointGrid}>
            <View style={[styles.breakpointItem, isSmallScreen && styles.activeBreakpoint]}>
              <Text style={[styles.breakpointText, isSmallScreen && styles.activeBreakpointText]}>
                Small (&lt; 768px)
              </Text>
              <Text style={styles.breakpointStatus}>
                {isSmallScreen ? '✅ Active' : '❌ Inactive'}
              </Text>
            </View>
            <View style={[styles.breakpointItem, isMediumScreen && styles.activeBreakpoint]}>
              <Text style={[styles.breakpointText, isMediumScreen && styles.activeBreakpointText]}>
                Medium (768-1024px)
              </Text>
              <Text style={styles.breakpointStatus}>
                {isMediumScreen ? '✅ Active' : '❌ Inactive'}
              </Text>
            </View>
            <View style={[styles.breakpointItem, isLargeScreen && styles.activeBreakpoint]}>
              <Text style={[styles.breakpointText, isLargeScreen && styles.activeBreakpointText]}>
                Large (&gt; 1024px)
              </Text>
              <Text style={styles.breakpointStatus}>
                {isLargeScreen ? '✅ Active' : '❌ Inactive'}
              </Text>
            </View>
          </View>
        </Card>

        {/* Responsive Time Slots Test */}
        <Card style={styles.timeSlotsCard}>
          <Text style={styles.cardTitle}>Responsive Time Slots</Text>
          <Text style={styles.cardSubtitle}>
            This component adapts to different screen sizes automatically
          </Text>
          <ResponsiveTimeSlots
            slots={mockTimeSlots}
            selectedSlot={selectedSlot}
            onSlotSelect={setSelectedSlot}
          />
          {selectedSlot && (
            <View style={styles.selectedInfo}>
              <Text style={styles.selectedText}>
                Selected: {selectedSlot.start_time} - {selectedSlot.end_time}
              </Text>
            </View>
          )}
        </Card>

        {/* Layout Test */}
        <Card style={styles.layoutCard}>
          <Text style={styles.cardTitle}>Layout Adaptation</Text>
          <View style={[
            styles.layoutGrid,
            { flexDirection: isTablet ? 'row' : 'column' }
          ]}>
            <View style={[
              styles.layoutItem,
              { flex: isTablet ? 1 : undefined, marginRight: isTablet ? 16 : 0 }
            ]}>
              <Text style={styles.layoutItemTitle}>Content Block 1</Text>
              <Text style={styles.layoutItemText}>
                This content adapts its layout based on screen size.
                On tablets, it displays in a row. On phones, it stacks vertically.
              </Text>
            </View>
            <View style={[
              styles.layoutItem,
              { flex: isTablet ? 1 : undefined, marginTop: isTablet ? 0 : 16 }
            ]}>
              <Text style={styles.layoutItemTitle}>Content Block 2</Text>
              <Text style={styles.layoutItemText}>
                Font sizes, padding, and spacing all scale appropriately
                for the current device and screen size.
              </Text>
            </View>
          </View>
        </Card>

        {/* Button Test */}
        <Card style={styles.buttonCard}>
          <Text style={styles.cardTitle}>Responsive Buttons</Text>
          <TouchableOpacity style={[
            styles.testButton,
            { paddingVertical: isTablet ? 16 : 12 }
          ]}>
            <Text style={[
              styles.testButtonText,
              { fontSize: isTablet ? 18 : 16 }
            ]}>
              Responsive Button
            </Text>
          </TouchableOpacity>
        </Card>

        {/* Instructions */}
        <Card style={styles.instructionsCard}>
          <Text style={styles.cardTitle}>Testing Instructions</Text>
          <Text style={styles.instructionText}>
            1. Resize your browser window to test different breakpoints
          </Text>
          <Text style={styles.instructionText}>
            2. Try rotating your device (if on mobile)
          </Text>
          <Text style={styles.instructionText}>
            3. Test on different devices: phone, tablet, desktop
          </Text>
          <Text style={styles.instructionText}>
            4. Check that time slots adapt their grid layout
          </Text>
          <Text style={styles.instructionText}>
            5. Verify that text sizes and spacing scale appropriately
          </Text>
        </Card>
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f9fafb',
  },
  content: {
    paddingVertical: 60,
  },
  title: {
    fontWeight: 'bold',
    color: '#111827',
    textAlign: 'center',
    marginBottom: 32,
  },
  infoCard: {
    marginBottom: 24,
  },
  cardTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#111827',
    marginBottom: 16,
  },
  cardSubtitle: {
    fontSize: 14,
    color: '#6b7280',
    marginBottom: 16,
  },
  infoGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  infoItem: {
    width: '48%',
    marginBottom: 12,
  },
  infoLabel: {
    fontSize: 14,
    color: '#6b7280',
    marginBottom: 4,
  },
  infoValue: {
    fontSize: 16,
    fontWeight: '600',
    color: '#111827',
  },
  breakpointCard: {
    marginBottom: 24,
  },
  breakpointGrid: {
    gap: 12,
  },
  breakpointItem: {
    padding: 12,
    borderRadius: 8,
    backgroundColor: '#f3f4f6',
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  activeBreakpoint: {
    backgroundColor: '#dbeafe',
    borderWidth: 1,
    borderColor: '#2563eb',
  },
  breakpointText: {
    fontSize: 14,
    color: '#6b7280',
  },
  activeBreakpointText: {
    color: '#2563eb',
    fontWeight: '600',
  },
  breakpointStatus: {
    fontSize: 12,
    fontWeight: '500',
  },
  timeSlotsCard: {
    marginBottom: 24,
  },
  selectedInfo: {
    marginTop: 16,
    padding: 12,
    backgroundColor: '#f0f9ff',
    borderRadius: 8,
  },
  selectedText: {
    fontSize: 14,
    color: '#0369a1',
    fontWeight: '500',
  },
  layoutCard: {
    marginBottom: 24,
  },
  layoutGrid: {
    gap: 16,
  },
  layoutItem: {
    padding: 16,
    backgroundColor: '#f8fafc',
    borderRadius: 8,
  },
  layoutItemTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#111827',
    marginBottom: 8,
  },
  layoutItemText: {
    fontSize: 14,
    color: '#6b7280',
    lineHeight: 20,
  },
  buttonCard: {
    marginBottom: 24,
  },
  testButton: {
    backgroundColor: '#2563eb',
    borderRadius: 8,
    alignItems: 'center',
  },
  testButtonText: {
    color: '#ffffff',
    fontWeight: '600',
  },
  instructionsCard: {
    marginBottom: 24,
  },
  instructionText: {
    fontSize: 14,
    color: '#374151',
    marginBottom: 8,
    lineHeight: 20,
  },
});
