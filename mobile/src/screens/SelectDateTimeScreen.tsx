import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
} from 'react-native';
import { useNavigation, useRoute } from '@react-navigation/native';
import { Card, Loading, ResponsiveTimeSlots } from '../components';
import { Doctor, TimeSlot } from '../types';
import { formatDate, formatTime } from '../utils/helpers';
import { useAuth } from '../context/AuthContext';
import ApiService from '../services/api';
import { useResponsive } from '../hooks/useResponsive';

interface RouteParams {
  doctor: Doctor;
}

export const SelectDateTimeScreen: React.FC = () => {
  const navigation = useNavigation();
  const route = useRoute();
  const { doctor } = route.params as RouteParams;
  const { selectedPatient } = useAuth();
  const { isTablet, width } = useResponsive();

  const [selectedSlot, setSelectedSlot] = useState<TimeSlot | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [groupedSlots, setGroupedSlots] = useState<{ [date: string]: TimeSlot[] }>({});

  useEffect(() => {
    loadAvailableSlots();
  }, []);

  const loadAvailableSlots = async () => {
    try {
      console.log('Loading available slots for doctor:', doctor.id);
      const response = await ApiService.getAvailableSlots(doctor.id);
      console.log('Available slots response:', response);

      if (response.success && response.data) {
        groupSlotsByDate(response.data);
      } else {
        console.warn('No slots available or API error:', response.message);
        Alert.alert('No Slots Available', 'No appointment slots are currently available for this doctor.');
      }
    } catch (error) {
      console.error('Error loading available slots:', error);
      Alert.alert(
        'Connection Error',
        'Unable to load appointment slots. Please check your internet connection and try again.'
      );
    } finally {
      setIsLoading(false);
    }
  };

  const groupSlotsByDate = (slots: TimeSlot[]) => {
    const grouped = slots.reduce((acc, slot) => {
      const date = slot.date;
      if (!acc[date]) {
        acc[date] = [];
      }
      acc[date].push(slot);
      return acc;
    }, {} as { [date: string]: TimeSlot[] });

    setGroupedSlots(grouped);
  };

  const handleSlotSelect = (slot: TimeSlot) => {
    setSelectedSlot(slot);
  };

  const handleContinue = () => {
    if (!selectedSlot || !selectedPatient) {
      Alert.alert('Error', 'Please select a time slot');
      return;
    }

    (navigation as any).navigate('ConfirmAppointment', {
      doctor,
      timeSlot: selectedSlot,
      patient: selectedPatient,
    });
  };

  if (isLoading) {
    return <Loading message="Loading available slots..." />;
  }

  const dates = Object.keys(groupedSlots).sort();

  const dynamicStyles = {
    scrollContent: {
      padding: isTablet ? 32 : 24,
      paddingBottom: 20,
    },
    header: {
      padding: isTablet ? 32 : 24,
      paddingTop: isTablet ? 80 : 60,
    },
    bottomSection: {
      padding: isTablet ? 32 : 24,
    },
  };

  return (
    <View style={styles.container}>
      {/* Header */}
      <View style={[styles.header, dynamicStyles.header]}>
        <TouchableOpacity onPress={() => navigation.goBack()}>
          <Text style={styles.backButton}>← Back</Text>
        </TouchableOpacity>
        <Text style={[styles.title, { fontSize: isTablet ? 24 : 20 }]}>
          Select Date & Time
        </Text>
      </View>

      {/* Main Content Area */}
      <View style={styles.mainContent}>
        <ScrollView
          style={styles.scrollView}
          contentContainerStyle={[styles.scrollContent, dynamicStyles.scrollContent]}
          showsVerticalScrollIndicator={false}
        >
          <Card style={styles.doctorCard}>
            <Text style={styles.doctorName}>Dr. {doctor.name}</Text>
            <Text style={styles.specialty}>{doctor.specialty}</Text>
          </Card>

          {dates.length > 0 ? (
            dates.map((date) => (
              <View key={date} style={styles.dateSection}>
                <Text style={[
                  styles.dateHeader,
                  { fontSize: isTablet ? 20 : 18 }
                ]}>
                  {formatDate(date, 'EEEE, MMM dd')}
                </Text>

                <ResponsiveTimeSlots
                  slots={groupedSlots[date]}
                  selectedSlot={selectedSlot}
                  onSlotSelect={handleSlotSelect}
                />
              </View>
            ))
          ) : (
            <View style={styles.emptyState}>
              <Text style={styles.emptyIcon}>📅</Text>
              <Text style={styles.emptyTitle}>No Available Slots</Text>
              <Text style={styles.emptyDescription}>
                Dr. {doctor.name} doesn't have any available slots at the moment.
                Please try another doctor or check back later.
              </Text>
            </View>
          )}
        </ScrollView>
      </View>

      {/* Fixed Bottom Section */}
      {selectedSlot && (
        <View style={[styles.bottomSection, dynamicStyles.bottomSection]}>
          <View style={styles.selectedSlotInfo}>
            <Text style={styles.selectedSlotLabel}>Selected:</Text>
            <Text style={styles.selectedSlotDetails}>
              {formatDate(selectedSlot.date, 'MMM dd')} at {formatTime(selectedSlot.start_time)}
            </Text>
          </View>
          <TouchableOpacity style={styles.continueButton} onPress={handleContinue}>
            <Text style={styles.continueButtonText}>Continue</Text>
          </TouchableOpacity>
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f9fafb',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#ffffff',
    borderBottomWidth: 1,
    borderBottomColor: '#e5e7eb',
  },
  mainContent: {
    flex: 1,
  },
  backButton: {
    fontSize: 16,
    color: '#2563eb',
    fontWeight: '500',
    marginRight: 16,
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#111827',
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    padding: 24,
    paddingBottom: 20,
  },
  bottomSection: {
    backgroundColor: '#ffffff',
    borderTopWidth: 1,
    borderTopColor: '#e5e7eb',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: -2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
  },
  doctorCard: {
    marginBottom: 24,
    alignItems: 'center',
  },
  doctorName: {
    fontSize: 20,
    fontWeight: '600',
    color: '#111827',
    marginBottom: 4,
  },
  specialty: {
    fontSize: 16,
    color: '#6b7280',
  },
  dateSection: {
    marginBottom: 24,
  },
  dateHeader: {
    fontSize: 18,
    fontWeight: '600',
    color: '#111827',
    marginBottom: 12,
  },


  selectedSlotInfo: {
    marginBottom: 16,
  },
  selectedSlotLabel: {
    fontSize: 14,
    color: '#6b7280',
    marginBottom: 4,
  },
  selectedSlotDetails: {
    fontSize: 16,
    fontWeight: '600',
    color: '#111827',
  },
  continueButton: {
    backgroundColor: '#2563eb',
    paddingVertical: 16,
    borderRadius: 8,
    alignItems: 'center',
  },
  continueButtonText: {
    color: '#ffffff',
    fontSize: 16,
    fontWeight: '600',
  },
  emptyState: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 32,
  },
  emptyIcon: {
    fontSize: 64,
    marginBottom: 16,
  },
  emptyTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: '#111827',
    marginBottom: 8,
    textAlign: 'center',
  },
  emptyDescription: {
    fontSize: 16,
    color: '#6b7280',
    textAlign: 'center',
    lineHeight: 24,
  },
});
