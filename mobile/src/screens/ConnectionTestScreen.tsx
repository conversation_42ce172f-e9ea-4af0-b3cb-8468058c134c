import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  Alert,
} from 'react-native';
import { Card, NetworkStatus } from '../components';
import ApiService from '../services/api';

export const ConnectionTestScreen: React.FC = () => {
  const [testResults, setTestResults] = useState<{
    health: boolean | null;
    specialties: boolean | null;
    doctors: boolean | null;
    slots: boolean | null;
  }>({
    health: null,
    specialties: null,
    doctors: null,
    slots: null,
  });
  const [isRunning, setIsRunning] = useState(false);

  const runTests = async () => {
    setIsRunning(true);
    setTestResults({
      health: null,
      specialties: null,
      doctors: null,
      slots: null,
    });

    try {
      // Test 1: Health check
      console.log('Testing health endpoint...');
      const healthCheck = await ApiService.checkServerHealth();
      setTestResults(prev => ({ ...prev, health: healthCheck }));

      if (!healthCheck) {
        setIsRunning(false);
        return;
      }

      // Test 2: Specialties
      console.log('Testing specialties endpoint...');
      try {
        const specialtiesResponse = await ApiService.getSpecialties();
        setTestResults(prev => ({ ...prev, specialties: specialtiesResponse.success }));
      } catch (error) {
        setTestResults(prev => ({ ...prev, specialties: false }));
      }

      // Test 3: Doctors
      console.log('Testing doctors endpoint...');
      try {
        const doctorsResponse = await ApiService.getDoctorsBySpecialty();
        setTestResults(prev => ({ ...prev, doctors: doctorsResponse.success }));
      } catch (error) {
        setTestResults(prev => ({ ...prev, doctors: false }));
      }

      // Test 4: Time slots
      console.log('Testing time slots endpoint...');
      try {
        const slotsResponse = await ApiService.getAvailableSlots('1');
        setTestResults(prev => ({ ...prev, slots: slotsResponse.success }));
      } catch (error) {
        setTestResults(prev => ({ ...prev, slots: false }));
      }

    } catch (error) {
      console.error('Test error:', error);
    } finally {
      setIsRunning(false);
    }
  };

  const getStatusIcon = (status: boolean | null) => {
    if (status === null) return '⏳';
    return status ? '✅' : '❌';
  };

  const getStatusText = (status: boolean | null) => {
    if (status === null) return 'Pending';
    return status ? 'Success' : 'Failed';
  };

  const showTroubleshootingTips = () => {
    Alert.alert(
      'Troubleshooting Tips',
      '1. Make sure the backend server is running:\n   cd backend && node server-with-data.js\n\n2. Check that you\'re connected to the same network\n\n3. Verify the API URL in mobile/src/services/api.ts\n\n4. For iOS Simulator: Use localhost\n   For Android Emulator: Use ********\n   For Physical Device: Use your computer\'s IP address',
      [{ text: 'OK' }]
    );
  };

  return (
    <ScrollView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>Connection Test</Text>
        <Text style={styles.subtitle}>Test API connectivity and endpoints</Text>
      </View>

      <NetworkStatus onRetry={runTests} />

      <Card style={styles.testCard}>
        <Text style={styles.cardTitle}>API Endpoint Tests</Text>
        
        <View style={styles.testItem}>
          <Text style={styles.testName}>Health Check</Text>
          <Text style={styles.testStatus}>
            {getStatusIcon(testResults.health)} {getStatusText(testResults.health)}
          </Text>
        </View>

        <View style={styles.testItem}>
          <Text style={styles.testName}>Specialties</Text>
          <Text style={styles.testStatus}>
            {getStatusIcon(testResults.specialties)} {getStatusText(testResults.specialties)}
          </Text>
        </View>

        <View style={styles.testItem}>
          <Text style={styles.testName}>Doctors</Text>
          <Text style={styles.testStatus}>
            {getStatusIcon(testResults.doctors)} {getStatusText(testResults.doctors)}
          </Text>
        </View>

        <View style={styles.testItem}>
          <Text style={styles.testName}>Time Slots</Text>
          <Text style={styles.testStatus}>
            {getStatusIcon(testResults.slots)} {getStatusText(testResults.slots)}
          </Text>
        </View>

        <TouchableOpacity 
          style={[styles.testButton, isRunning && styles.testButtonDisabled]} 
          onPress={runTests}
          disabled={isRunning}
        >
          <Text style={styles.testButtonText}>
            {isRunning ? 'Running Tests...' : 'Run Tests'}
          </Text>
        </TouchableOpacity>
      </Card>

      <Card style={styles.infoCard}>
        <Text style={styles.cardTitle}>Connection Info</Text>
        <Text style={styles.infoText}>API Base URL: http://localhost:3000/api</Text>
        <Text style={styles.infoText}>Backend Port: 3000</Text>
        <Text style={styles.infoText}>Frontend Port: 8081</Text>
        
        <TouchableOpacity style={styles.helpButton} onPress={showTroubleshootingTips}>
          <Text style={styles.helpButtonText}>Troubleshooting Tips</Text>
        </TouchableOpacity>
      </Card>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f9fafb',
  },
  header: {
    padding: 24,
    paddingTop: 60,
    backgroundColor: '#ffffff',
    borderBottomWidth: 1,
    borderBottomColor: '#e5e7eb',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#111827',
  },
  subtitle: {
    fontSize: 14,
    color: '#6b7280',
    marginTop: 4,
  },
  testCard: {
    margin: 16,
  },
  infoCard: {
    margin: 16,
    marginTop: 0,
  },
  cardTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#111827',
    marginBottom: 16,
  },
  testItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 8,
    borderBottomWidth: 1,
    borderBottomColor: '#f3f4f6',
  },
  testName: {
    fontSize: 16,
    color: '#374151',
  },
  testStatus: {
    fontSize: 14,
    fontWeight: '500',
  },
  testButton: {
    backgroundColor: '#2563eb',
    padding: 12,
    borderRadius: 8,
    alignItems: 'center',
    marginTop: 16,
  },
  testButtonDisabled: {
    backgroundColor: '#9ca3af',
  },
  testButtonText: {
    color: '#ffffff',
    fontSize: 16,
    fontWeight: '500',
  },
  infoText: {
    fontSize: 14,
    color: '#6b7280',
    marginBottom: 8,
  },
  helpButton: {
    backgroundColor: '#f3f4f6',
    padding: 12,
    borderRadius: 8,
    alignItems: 'center',
    marginTop: 12,
  },
  helpButtonText: {
    color: '#374151',
    fontSize: 14,
    fontWeight: '500',
  },
});
