import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  Alert,
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { Card, Loading, NetworkStatus } from '../components';
import ApiService from '../services/api';

const SPECIALTY_ICONS: { [key: string]: string } = {
  'Cardiology': '❤️',
  'Neurology': '🧠',
  'Pediatrics': '👶',
  'Orthopedics': '🦴',
  'Dermatology': '🧴',
  'Internal Medicine': '🩺',
  'Gynecology': '👩‍⚕️',
  'Psychiatry': '🧘',
  'Oncology': '🎗️',
  'Radiology': '📸',
  'Emergency Medicine': '🚑',
  'Anesthesiology': '💉',
};

export const SelectSpecialtyScreen: React.FC = () => {
  const navigation = useNavigation();
  const [specialties, setSpecialties] = useState<string[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    loadSpecialties();
  }, []);

  const loadSpecialties = async () => {
    try {
      console.log('Loading specialties...');
      const response = await ApiService.getSpecialties();
      console.log('Specialties response:', response);

      if (response.success && response.data) {
        setSpecialties(response.data);
      } else {
        console.warn('No specialties found or API error:', response.message);
        Alert.alert('No Specialties', 'No medical specialties are currently available.');
      }
    } catch (error) {
      console.error('Error loading specialties:', error);
      Alert.alert(
        'Connection Error',
        'Unable to load medical specialties. Please check your internet connection and try again.'
      );
    } finally {
      setIsLoading(false);
    }
  };

  const handleSelectSpecialty = (specialty: string) => {
    (navigation as any).navigate('SelectDoctor', { specialty });
  };

  const renderSpecialtyCard = ({ item }: { item: string }) => (
    <Card
      onPress={() => handleSelectSpecialty(item)}
      style={styles.specialtyCard}
    >
      <View style={styles.specialtyContent}>
        <Text style={styles.specialtyIcon}>
          {SPECIALTY_ICONS[item] || '🏥'}
        </Text>
        <Text style={styles.specialtyName}>{item}</Text>
        <Text style={styles.arrow}>→</Text>
      </View>
    </Card>
  );

  if (isLoading) {
    return <Loading message="Loading specialties..." />;
  }

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity onPress={() => navigation.goBack()}>
          <Text style={styles.backButton}>← Back</Text>
        </TouchableOpacity>
        <Text style={styles.title}>Select Specialty</Text>
      </View>

      <View style={styles.content}>
        <Text style={styles.subtitle}>
          Choose the medical specialty for your appointment
        </Text>

        <NetworkStatus onRetry={loadSpecialties} />

        {specialties.length > 0 ? (
          <FlatList
            data={specialties}
            renderItem={renderSpecialtyCard}
            keyExtractor={(item) => item}
            showsVerticalScrollIndicator={false}
            contentContainerStyle={styles.listContainer}
          />
        ) : (
          <View style={styles.emptyState}>
            <Text style={styles.emptyIcon}>🏥</Text>
            <Text style={styles.emptyTitle}>No Specialties Available</Text>
            <Text style={styles.emptyDescription}>
              Unable to load medical specialties. Please check your connection and try again.
            </Text>
          </View>
        )}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f9fafb',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 24,
    paddingTop: 60,
    backgroundColor: '#ffffff',
    borderBottomWidth: 1,
    borderBottomColor: '#e5e7eb',
  },
  backButton: {
    fontSize: 16,
    color: '#2563eb',
    fontWeight: '500',
    marginRight: 16,
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#111827',
  },
  content: {
    flex: 1,
    padding: 24,
  },
  subtitle: {
    fontSize: 16,
    color: '#6b7280',
    marginBottom: 24,
    textAlign: 'center',
  },
  listContainer: {
    paddingBottom: 24,
  },
  specialtyCard: {
    marginBottom: 12,
  },
  specialtyContent: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
  },
  specialtyIcon: {
    fontSize: 32,
    marginRight: 16,
    width: 40,
    textAlign: 'center',
  },
  specialtyName: {
    flex: 1,
    fontSize: 18,
    fontWeight: '500',
    color: '#111827',
  },
  arrow: {
    fontSize: 20,
    color: '#9ca3af',
    marginLeft: 8,
  },
  emptyState: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 32,
  },
  emptyIcon: {
    fontSize: 64,
    marginBottom: 16,
  },
  emptyTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: '#111827',
    marginBottom: 8,
    textAlign: 'center',
  },
  emptyDescription: {
    fontSize: 16,
    color: '#6b7280',
    textAlign: 'center',
    lineHeight: 24,
  },
});
