import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { Card } from '../components';
import { useAuth } from '../context/AuthContext';
import { getInitials } from '../utils/helpers';
import { useResponsive } from '../hooks/useResponsive';

export const BookAppointmentScreen: React.FC = () => {
  const navigation = useNavigation();
  const { selectedPatient } = useAuth();
  const { isTablet, width } = useResponsive();

  const handleSelectSpecialty = () => {
    navigation.navigate('SelectSpecialty' as never);
  };

  const dynamicStyles = {
    header: {
      padding: isTablet ? 32 : 24,
      paddingTop: isTablet ? 80 : 60,
    },
    scrollContent: {
      padding: isTablet ? 32 : 24,
      paddingBottom: 20,
      maxWidth: isTablet ? 800 : '100%',
      alignSelf: 'center' as const,
      width: '100%',
    },
    bottomSection: {
      padding: isTablet ? 32 : 24,
      maxWidth: isTablet ? 800 : '100%',
      alignSelf: 'center' as const,
      width: '100%',
    },
  };

  if (!selectedPatient) {
    return (
      <View style={styles.container}>
        <Text style={styles.noPatientText}>No patient selected</Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      {/* Header */}
      <View style={[styles.header, dynamicStyles.header]}>
        <TouchableOpacity onPress={() => navigation.goBack()}>
          <Text style={[styles.backButton, { fontSize: isTablet ? 18 : 16 }]}>← Back</Text>
        </TouchableOpacity>
        <Text style={[styles.title, { fontSize: isTablet ? 24 : 20 }]}>Book Appointment</Text>
      </View>

      {/* Main Content */}
      <View style={styles.mainContent}>
        <ScrollView
          style={styles.scrollView}
          contentContainerStyle={[styles.scrollContent, dynamicStyles.scrollContent]}
          showsVerticalScrollIndicator={false}
        >
        {/* Patient Info */}
        <Card style={styles.patientCard}>
          <Text style={styles.sectionTitle}>Booking for:</Text>
          <View style={styles.patientInfo}>
            <View style={styles.avatar}>
              <Text style={styles.avatarText}>{getInitials(selectedPatient.name)}</Text>
            </View>
            <View style={styles.patientDetails}>
              <Text style={styles.patientName}>{selectedPatient.name}</Text>
              <Text style={styles.patientMeta}>
                {selectedPatient.age} years • {selectedPatient.gender}
              </Text>
            </View>
          </View>
        </Card>

        {/* Booking Steps */}
        <Card style={styles.stepsCard}>
          <Text style={styles.sectionTitle}>Booking Process</Text>
          
          <View style={styles.step}>
            <View style={styles.stepNumber}>
              <Text style={styles.stepNumberText}>1</Text>
            </View>
            <View style={styles.stepContent}>
              <Text style={styles.stepTitle}>Select Specialty</Text>
              <Text style={styles.stepDescription}>
                Choose the medical specialty you need
              </Text>
            </View>
          </View>

          <View style={styles.step}>
            <View style={styles.stepNumber}>
              <Text style={styles.stepNumberText}>2</Text>
            </View>
            <View style={styles.stepContent}>
              <Text style={styles.stepTitle}>Choose Doctor</Text>
              <Text style={styles.stepDescription}>
                Select from available specialists
              </Text>
            </View>
          </View>

          <View style={styles.step}>
            <View style={styles.stepNumber}>
              <Text style={styles.stepNumberText}>3</Text>
            </View>
            <View style={styles.stepContent}>
              <Text style={styles.stepTitle}>Pick Date & Time</Text>
              <Text style={styles.stepDescription}>
                Choose your preferred appointment slot
              </Text>
            </View>
          </View>

          <View style={styles.step}>
            <View style={styles.stepNumber}>
              <Text style={styles.stepNumberText}>4</Text>
            </View>
            <View style={styles.stepContent}>
              <Text style={styles.stepTitle}>Confirm & Pay</Text>
              <Text style={styles.stepDescription}>
                Review details and complete booking
              </Text>
            </View>
          </View>
        </Card>

        {/* Important Notes */}
        <Card style={styles.notesCard}>
          <Text style={styles.sectionTitle}>Important Notes</Text>
          
          <View style={styles.note}>
            <Text style={styles.noteIcon}>⏰</Text>
            <Text style={styles.noteText}>
              Please arrive 15 minutes before your appointment time
            </Text>
          </View>

          <View style={styles.note}>
            <Text style={styles.noteIcon}>💳</Text>
            <Text style={styles.noteText}>
              Consultation fee: $500 (payment required at booking)
            </Text>
          </View>

          <View style={styles.note}>
            <Text style={styles.noteIcon}>📋</Text>
            <Text style={styles.noteText}>
              Bring your ID and any relevant medical documents
            </Text>
          </View>

          <View style={styles.note}>
            <Text style={styles.noteIcon}>📞</Text>
            <Text style={styles.noteText}>
              You can reschedule up to 24 hours before your appointment
            </Text>
          </View>
        </Card>
        </ScrollView>
      </View>

      {/* Fixed Bottom Button */}
      <View style={[styles.bottomSection, dynamicStyles.bottomSection]}>
        <TouchableOpacity style={styles.startButton} onPress={handleSelectSpecialty}>
          <Text style={[styles.startButtonText, { fontSize: isTablet ? 20 : 18 }]}>Start Booking</Text>
        </TouchableOpacity>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f9fafb',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#ffffff',
    borderBottomWidth: 1,
    borderBottomColor: '#e5e7eb',
  },
  backButton: {
    fontSize: 16,
    color: '#2563eb',
    fontWeight: '500',
    marginRight: 16,
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#111827',
  },
  mainContent: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    paddingBottom: 20,
  },
  bottomSection: {
    backgroundColor: '#ffffff',
    borderTopWidth: 1,
    borderTopColor: '#e5e7eb',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: -2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#111827',
    marginBottom: 16,
  },
  patientCard: {
    marginBottom: 16,
  },
  patientInfo: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  avatar: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: '#2563eb',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  avatarText: {
    color: '#ffffff',
    fontSize: 18,
    fontWeight: 'bold',
  },
  patientDetails: {
    flex: 1,
  },
  patientName: {
    fontSize: 18,
    fontWeight: '600',
    color: '#111827',
    marginBottom: 4,
  },
  patientMeta: {
    fontSize: 14,
    color: '#6b7280',
  },
  stepsCard: {
    marginBottom: 16,
  },
  step: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: 20,
    paddingHorizontal: 4,
  },
  stepNumber: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: '#2563eb',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  stepNumberText: {
    color: '#ffffff',
    fontSize: 14,
    fontWeight: 'bold',
  },
  stepContent: {
    flex: 1,
  },
  stepTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#111827',
    marginBottom: 4,
  },
  stepDescription: {
    fontSize: 14,
    color: '#6b7280',
    lineHeight: 20,
  },
  notesCard: {
    marginBottom: 24,
  },
  note: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: 16,
    paddingHorizontal: 4,
  },
  noteIcon: {
    fontSize: 16,
    marginRight: 12,
    marginTop: 2,
    width: 20,
    textAlign: 'center',
  },
  noteText: {
    flex: 1,
    fontSize: 14,
    color: '#374151',
    lineHeight: 22,
  },
  startButton: {
    backgroundColor: '#2563eb',
    paddingVertical: 16,
    borderRadius: 8,
    alignItems: 'center',
  },
  startButtonText: {
    color: '#ffffff',
    fontSize: 18,
    fontWeight: '600',
  },
  noPatientText: {
    fontSize: 18,
    color: '#6b7280',
    textAlign: 'center',
    marginTop: 100,
  },
});
