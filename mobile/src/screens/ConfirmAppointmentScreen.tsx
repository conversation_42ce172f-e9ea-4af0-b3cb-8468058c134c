import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
} from 'react-native';
import { useNavigation, useRoute } from '@react-navigation/native';
import { Card, Input, Button } from '../components';
import { Doctor, TimeSlot, Patient } from '../types';
import { formatDate, formatTime, getInitials } from '../utils/helpers';
import ApiService from '../services/api';

interface RouteParams {
  doctor: Doctor;
  timeSlot: TimeSlot;
  patient: Patient;
}

export const ConfirmAppointmentScreen: React.FC = () => {
  const navigation = useNavigation();
  const route = useRoute();
  const { doctor, timeSlot, patient } = route.params as RouteParams;
  
  const [notes, setNotes] = useState('');
  const [isBooking, setIsBooking] = useState(false);

  const consultationFee = 500; // Fixed fee for demo

  const handleBookAppointment = async () => {
    setIsBooking(true);

    try {
      console.log('Booking appointment with data:', {
        patient_id: patient.id,
        doctor_id: doctor.id,
        time_slot_id: timeSlot.id,
        notes: notes.trim() || undefined,
      });

      const response = await ApiService.bookAppointment({
        patient_id: patient.id,
        doctor_id: doctor.id,
        time_slot_id: timeSlot.id,
        notes: notes.trim() || undefined,
      });

      console.log('Booking response:', response);

      if (response.success) {
        Alert.alert(
          'Success!',
          `Your appointment has been booked successfully!\n\nAppointment ID: ${response.data?.id || 'N/A'}\nDate: ${timeSlot.date}\nTime: ${timeSlot.start_time}`,
          [
            {
              text: 'OK',
              onPress: () => {
                // Navigate back to appointments screen
                (navigation as any).navigate('Main', { screen: 'Appointments' });
              },
            },
          ]
        );
      } else {
        Alert.alert('Booking Failed', response.message || 'Failed to book appointment. Please try again.');
      }
    } catch (error) {
      console.error('Appointment booking error:', error);
      const errorMessage = error instanceof Error ? error.message : 'Something went wrong. Please try again.';
      Alert.alert('Connection Error', errorMessage);
    } finally {
      setIsBooking(false);
    }
  };

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity onPress={() => navigation.goBack()}>
          <Text style={styles.backButton}>← Back</Text>
        </TouchableOpacity>
        <Text style={styles.title}>Confirm Appointment</Text>
      </View>

      <ScrollView
        style={styles.content}
        contentContainerStyle={styles.scrollContent}
      >
        {/* Patient Info */}
        <Card style={styles.section}>
          <Text style={styles.sectionTitle}>Patient</Text>
          <View style={styles.patientInfo}>
            <View style={styles.avatar}>
              <Text style={styles.avatarText}>{getInitials(patient.name)}</Text>
            </View>
            <View style={styles.patientDetails}>
              <Text style={styles.patientName}>{patient.name}</Text>
              <Text style={styles.patientMeta}>
                {patient.age} years • {patient.gender}
              </Text>
            </View>
          </View>
        </Card>

        {/* Doctor Info */}
        <Card style={styles.section}>
          <Text style={styles.sectionTitle}>Doctor</Text>
          <View style={styles.doctorInfo}>
            <View style={styles.avatar}>
              <Text style={styles.avatarText}>{getInitials(doctor.name)}</Text>
            </View>
            <View style={styles.doctorDetails}>
              <Text style={styles.doctorName}>Dr. {doctor.name}</Text>
              <Text style={styles.specialty}>{doctor.specialty}</Text>
              <Text style={styles.experience}>
                {doctor.experience_years} years experience
              </Text>
            </View>
          </View>
        </Card>

        {/* Appointment Details */}
        <Card style={styles.section}>
          <Text style={styles.sectionTitle}>Appointment Details</Text>
          <View style={styles.appointmentDetails}>
            <View style={styles.detailRow}>
              <Text style={styles.detailLabel}>Date:</Text>
              <Text style={styles.detailValue}>
                {formatDate(timeSlot.date, 'EEEE, MMMM dd, yyyy')}
              </Text>
            </View>
            <View style={styles.detailRow}>
              <Text style={styles.detailLabel}>Time:</Text>
              <Text style={styles.detailValue}>
                {formatTime(timeSlot.start_time)} - {formatTime(timeSlot.end_time)}
              </Text>
            </View>
            <View style={styles.detailRow}>
              <Text style={styles.detailLabel}>Duration:</Text>
              <Text style={styles.detailValue}>30 minutes</Text>
            </View>
          </View>
        </Card>

        {/* Notes */}
        <Card style={styles.section}>
          <Text style={styles.sectionTitle}>Notes (Optional)</Text>
          <Input
            value={notes}
            onChangeText={setNotes}
            placeholder="Add any notes or symptoms you'd like to discuss..."
            multiline
            numberOfLines={4}
            style={styles.notesInput}
          />
        </Card>

        {/* Payment Summary */}
        <Card style={styles.section}>
          <Text style={styles.sectionTitle}>Payment Summary</Text>
          <View style={styles.paymentDetails}>
            <View style={styles.paymentRow}>
              <Text style={styles.paymentLabel}>Consultation Fee:</Text>
              <Text style={styles.paymentValue}>${consultationFee}</Text>
            </View>
            <View style={styles.paymentRow}>
              <Text style={styles.paymentLabel}>Platform Fee:</Text>
              <Text style={styles.paymentValue}>$0</Text>
            </View>
            <View style={[styles.paymentRow, styles.totalRow]}>
              <Text style={styles.totalLabel}>Total:</Text>
              <Text style={styles.totalValue}>${consultationFee}</Text>
            </View>
          </View>
        </Card>
      </ScrollView>

      <View style={styles.footer}>
        <Button
          title={isBooking ? 'Booking...' : 'Book Appointment'}
          onPress={handleBookAppointment}
          loading={isBooking}
          style={styles.bookButton}
        />
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f9fafb',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 24,
    paddingTop: 60,
    backgroundColor: '#ffffff',
    borderBottomWidth: 1,
    borderBottomColor: '#e5e7eb',
  },
  backButton: {
    fontSize: 16,
    color: '#2563eb',
    fontWeight: '500',
    marginRight: 16,
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#111827',
  },
  content: {
    flex: 1,
  },
  scrollContent: {
    paddingHorizontal: 24,
    paddingBottom: 120, // Space for footer
  },
  section: {
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#111827',
    marginBottom: 12,
  },
  patientInfo: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  avatar: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: '#2563eb',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  avatarText: {
    color: '#ffffff',
    fontSize: 18,
    fontWeight: 'bold',
  },
  patientDetails: {
    flex: 1,
  },
  patientName: {
    fontSize: 18,
    fontWeight: '600',
    color: '#111827',
    marginBottom: 4,
  },
  patientMeta: {
    fontSize: 14,
    color: '#6b7280',
  },
  doctorInfo: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  doctorDetails: {
    flex: 1,
  },
  doctorName: {
    fontSize: 18,
    fontWeight: '600',
    color: '#111827',
    marginBottom: 4,
  },
  specialty: {
    fontSize: 14,
    color: '#6b7280',
    marginBottom: 2,
  },
  experience: {
    fontSize: 12,
    color: '#9ca3af',
  },
  appointmentDetails: {},
  detailRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 8,
  },
  detailLabel: {
    fontSize: 14,
    color: '#6b7280',
  },
  detailValue: {
    fontSize: 14,
    fontWeight: '500',
    color: '#111827',
  },
  notesInput: {
    height: 100,
    textAlignVertical: 'top',
  },
  paymentDetails: {},
  paymentRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 8,
  },
  paymentLabel: {
    fontSize: 14,
    color: '#6b7280',
  },
  paymentValue: {
    fontSize: 14,
    fontWeight: '500',
    color: '#111827',
  },
  totalRow: {
    borderTopWidth: 1,
    borderTopColor: '#e5e7eb',
    paddingTop: 8,
    marginTop: 8,
  },
  totalLabel: {
    fontSize: 16,
    fontWeight: '600',
    color: '#111827',
  },
  totalValue: {
    fontSize: 16,
    fontWeight: '600',
    color: '#111827',
  },
  footer: {
    backgroundColor: '#ffffff',
    padding: 24,
    borderTopWidth: 1,
    borderTopColor: '#e5e7eb',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: -2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
  },
  bookButton: {
    backgroundColor: '#10b981',
  },
});
