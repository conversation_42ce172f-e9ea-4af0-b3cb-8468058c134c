import AsyncStorage from '@react-native-async-storage/async-storage';

/**
 * App Reset Utility
 * Handles clearing all app data and resetting to fresh state
 */

export class AppResetManager {
  private static readonly APP_VERSION_KEY = 'app_version';
  private static readonly CURRENT_VERSION = '1.0.1'; // Increment to force reset
  
  // All storage keys used by the app
  private static readonly STORAGE_KEYS = [
    'auth_token',
    'user',
    'patients',
    'selected_patient',
    'cached_specialties',
    'cached_doctors',
    'last_login_time',
    'appointment_cache',
    'medical_records_cache',
    'user_preferences',
    'onboarding_completed'
  ];

  /**
   * Check if app needs to be reset (fresh start or version change)
   */
  static async shouldResetApp(): Promise<boolean> {
    try {
      const storedVersion = await AsyncStorage.getItem(this.APP_VERSION_KEY);
      return !storedVersion || storedVersion !== this.CURRENT_VERSION;
    } catch (error) {
      console.error('Error checking app version:', error);
      return true; // Reset on error to be safe
    }
  }

  /**
   * Perform complete app reset
   */
  static async resetApp(): Promise<void> {
    try {
      console.log('🔄 Performing app reset...');
      
      // Clear all stored data
      await this.clearAllData();
      
      // Set current version
      await AsyncStorage.setItem(this.APP_VERSION_KEY, this.CURRENT_VERSION);
      
      console.log('✅ App reset completed');
    } catch (error) {
      console.error('❌ Error during app reset:', error);
      throw error;
    }
  }

  /**
   * Clear all stored data
   */
  static async clearAllData(): Promise<void> {
    try {
      await AsyncStorage.multiRemove(this.STORAGE_KEYS);
      console.log('🗑️ All stored data cleared');
    } catch (error) {
      console.error('Error clearing stored data:', error);
      throw error;
    }
  }

  /**
   * Force app reset (for manual reset)
   */
  static async forceReset(): Promise<void> {
    try {
      console.log('🔄 Force resetting app...');
      await this.clearAllData();
      await AsyncStorage.removeItem(this.APP_VERSION_KEY);
      console.log('✅ Force reset completed');
    } catch (error) {
      console.error('❌ Error during force reset:', error);
      throw error;
    }
  }

  /**
   * Get current app version
   */
  static async getCurrentVersion(): Promise<string> {
    try {
      const version = await AsyncStorage.getItem(this.APP_VERSION_KEY);
      return version || 'unknown';
    } catch (error) {
      console.error('Error getting app version:', error);
      return 'error';
    }
  }

  /**
   * Check if app is in fresh state
   */
  static async isFreshInstall(): Promise<boolean> {
    try {
      const hasAnyData = await Promise.all(
        this.STORAGE_KEYS.map(key => AsyncStorage.getItem(key))
      );
      return hasAnyData.every(value => value === null);
    } catch (error) {
      console.error('Error checking fresh install:', error);
      return true;
    }
  }

  /**
   * Get storage usage info (for debugging)
   */
  static async getStorageInfo(): Promise<{ [key: string]: string | null }> {
    try {
      const storageInfo: { [key: string]: string | null } = {};
      
      for (const key of this.STORAGE_KEYS) {
        storageInfo[key] = await AsyncStorage.getItem(key);
      }
      
      return storageInfo;
    } catch (error) {
      console.error('Error getting storage info:', error);
      return {};
    }
  }

  /**
   * Log current storage state (for debugging)
   */
  static async logStorageState(): Promise<void> {
    try {
      const storageInfo = await this.getStorageInfo();
      console.log('📱 Current Storage State:');
      
      for (const [key, value] of Object.entries(storageInfo)) {
        if (value) {
          console.log(`  ${key}: ${value.length > 100 ? value.substring(0, 100) + '...' : value}`);
        } else {
          console.log(`  ${key}: null`);
        }
      }
    } catch (error) {
      console.error('Error logging storage state:', error);
    }
  }
}

/**
 * Development helper to force reset
 */
export const DEV_RESET_APP = async () => {
  if (__DEV__) {
    await AppResetManager.forceReset();
    console.log('🔄 DEV: App has been reset. Please reload the app.');
  }
};

/**
 * Hook for app reset functionality
 */
export const useAppReset = () => {
  const resetApp = async () => {
    await AppResetManager.forceReset();
  };

  const checkAndReset = async () => {
    const shouldReset = await AppResetManager.shouldResetApp();
    if (shouldReset) {
      await AppResetManager.resetApp();
    }
    return shouldReset;
  };

  const getStorageInfo = async () => {
    return await AppResetManager.getStorageInfo();
  };

  return {
    resetApp,
    checkAndReset,
    getStorageInfo,
    logStorageState: AppResetManager.logStorageState,
  };
};
