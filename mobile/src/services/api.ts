import AsyncStorage from '@react-native-async-storage/async-storage';
import { 
  ApiResponse, 
  AuthResponse, 
  <PERSON>ient, 
  Doctor, 
  TimeSlot, 
  Appointment, 
  MedicalRecord,
  RegisterPatientForm 
} from '../types';

// Use localhost for web development
const API_BASE_URL = 'http://localhost:3000/api';

class ApiService {
  private async getAuthToken(): Promise<string | null> {
    return await AsyncStorage.getItem('auth_token');
  }

  // Check if the server is reachable
  async checkServerHealth(): Promise<boolean> {
    try {
      const response = await fetch(`${API_BASE_URL.replace('/api', '')}/health`, {
        method: 'GET',
        timeout: 5000,
      });
      return response.ok;
    } catch (error) {
      console.error('Server health check failed:', error);
      return false;
    }
  }

  private async makeRequest<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<ApiResponse<T>> {
    const token = await this.getAuthToken();

    const config: RequestInit = {
      headers: {
        'Content-Type': 'application/json',
        ...(token && { Authorization: `Bearer ${token}` }),
        ...options.headers,
      },
      timeout: 10000, // 10 second timeout
      ...options,
    };

    try {
      console.log('Making API request to:', `${API_BASE_URL}${endpoint}`);
      console.log('Request config:', { method: config.method || 'GET', headers: config.headers });

      const response = await fetch(`${API_BASE_URL}${endpoint}`, config);

      // Check if response is JSON
      const contentType = response.headers.get('content-type');
      let data;

      if (contentType && contentType.includes('application/json')) {
        data = await response.json();
      } else {
        const text = await response.text();
        console.warn('Non-JSON response received:', text);
        data = { success: false, message: 'Invalid response format' };
      }

      console.log('API Response:', {
        status: response.status,
        statusText: response.statusText,
        data
      });

      if (!response.ok) {
        // Handle different error types
        if (response.status === 401) {
          // Clear invalid token
          await AsyncStorage.removeItem('auth_token');
          throw new Error('Authentication failed. Please login again.');
        } else if (response.status === 403) {
          throw new Error('Access denied. Insufficient permissions.');
        } else if (response.status >= 500) {
          throw new Error('Server error. Please try again later.');
        } else {
          throw new Error(data.message || `Request failed with status ${response.status}`);
        }
      }

      return data;
    } catch (error) {
      console.error('API Request failed:', error);

      // Handle network errors
      if (error instanceof TypeError && error.message.includes('fetch')) {
        throw new Error('Network error. Please check your internet connection and ensure the server is running.');
      }

      throw error;
    }
  }

  // Auth endpoints
  async sendOTP(mobile_number: string): Promise<ApiResponse> {
    return this.makeRequest('/auth/send-otp', {
      method: 'POST',
      body: JSON.stringify({ mobile_number }),
    });
  }

  async verifyOTP(mobile_number: string, otp_code: string): Promise<ApiResponse<AuthResponse>> {
    const response = await this.makeRequest<AuthResponse>('/auth/verify-otp', {
      method: 'POST',
      body: JSON.stringify({ mobile_number, otp_code }),
    });

    // Store the token if verification is successful
    if (response.success && response.data?.token) {
      await AsyncStorage.setItem('auth_token', response.data.token);
      console.log('Auth token stored successfully');
    }

    return response;
  }

  // Logout method to clear stored token
  async logout(): Promise<void> {
    await AsyncStorage.removeItem('auth_token');
    console.log('Auth token cleared');
  }

  // Patient endpoints
  async getPatientsByMobile(): Promise<ApiResponse<Patient[]>> {
    return this.makeRequest('/patients/by-mobile');
  }

  async registerPatient(patientData: RegisterPatientForm): Promise<ApiResponse<Patient>> {
    const payload = {
      name: patientData.name,
      age: parseInt(patientData.age),
      gender: patientData.gender,
      date_of_birth: patientData.date_of_birth,
      contact_info: {
        email: patientData.email,
        mobile: patientData.mobile,
        emergency_contact: patientData.emergency_contact,
      },
      address: {
        street: patientData.street,
        city: patientData.city,
        state: patientData.state,
        postal_code: patientData.postal_code,
        country: patientData.country,
      },
    };

    return this.makeRequest('/patients/register', {
      method: 'POST',
      body: JSON.stringify(payload),
    });
  }

  async updateDemographics(
    patientId: string, 
    updates: { contact_info?: any; address?: any }
  ): Promise<ApiResponse<Patient>> {
    return this.makeRequest(`/patients/${patientId}/demographics`, {
      method: 'PUT',
      body: JSON.stringify(updates),
    });
  }

  async getMedicalRecords(patientId: string): Promise<ApiResponse<MedicalRecord[]>> {
    return this.makeRequest(`/patients/${patientId}/records`);
  }

  // Appointment endpoints
  async getSpecialties(): Promise<ApiResponse<string[]>> {
    return this.makeRequest('/appointments/specialties');
  }

  async getDoctorsBySpecialty(specialty?: string): Promise<ApiResponse<Doctor[]>> {
    const query = specialty ? `?specialty=${encodeURIComponent(specialty)}` : '';
    return this.makeRequest(`/appointments/doctors${query}`);
  }

  async getAvailableSlots(doctorId: string, date?: string): Promise<ApiResponse<TimeSlot[]>> {
    const query = date ? `?date=${date}` : '';
    return this.makeRequest(`/appointments/doctors/${doctorId}/slots${query}`);
  }

  async bookAppointment(appointmentData: {
    patient_id: string;
    doctor_id: string;
    time_slot_id: string;
    notes?: string;
  }): Promise<ApiResponse<Appointment>> {
    return this.makeRequest('/appointments/book', {
      method: 'POST',
      body: JSON.stringify(appointmentData),
    });
  }

  async getPatientAppointments(patientId: string): Promise<ApiResponse<Appointment[]>> {
    return this.makeRequest(`/appointments/patient/${patientId}`);
  }
}

export default new ApiService();
