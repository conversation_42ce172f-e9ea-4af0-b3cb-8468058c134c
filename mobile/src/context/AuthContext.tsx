import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { User, Patient, AuthContextType } from '../types';
import ApiService from '../services/api';
import { AppResetManager } from '../utils/appReset';

const AuthContext = createContext<AuthContextType | undefined>(undefined);

interface AuthProviderProps {
  children: ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [patients, setPatients] = useState<Patient[]>([]);
  const [selectedPatient, setSelectedPatient] = useState<Patient | null>(null);
  const [token, setToken] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    initializeApp();
  }, []);

  const initializeApp = async () => {
    try {
      // Check if app needs to be reset
      const shouldReset = await AppResetManager.shouldResetApp();

      if (shouldReset) {
        console.log('🔄 App reset required - clearing all data');
        await AppResetManager.resetApp();
      }

      // Log current storage state for debugging
      if (__DEV__) {
        await AppResetManager.logStorageState();
      }

      await loadStoredAuth();
    } catch (error) {
      console.error('Error initializing app:', error);
      setIsLoading(false);
    }
  };

  const loadStoredAuth = async () => {
    try {
      const storedToken = await AsyncStorage.getItem('auth_token');
      const storedUser = await AsyncStorage.getItem('user');
      const storedPatients = await AsyncStorage.getItem('patients');
      const storedSelectedPatient = await AsyncStorage.getItem('selected_patient');

      if (storedToken && storedUser) {
        setToken(storedToken);
        setUser(JSON.parse(storedUser));
        
        if (storedPatients) {
          const parsedPatients = JSON.parse(storedPatients);
          setPatients(parsedPatients);
          
          if (storedSelectedPatient) {
            setSelectedPatient(JSON.parse(storedSelectedPatient));
          }
        }
      }
    } catch (error) {
      console.error('Error loading stored auth:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const sendOTP = async (mobile_number: string): Promise<boolean> => {
    try {
      const response = await ApiService.sendOTP(mobile_number);
      return response.success;
    } catch (error) {
      console.error('Send OTP error:', error);
      return false;
    }
  };

  const login = async (mobile_number: string, otp_code: string): Promise<boolean> => {
    try {
      setIsLoading(true);
      const response = await ApiService.verifyOTP(mobile_number, otp_code);
      
      if (response.success && response.data) {
        const { token: authToken, user: userData, patients: patientsData } = response.data;
        
        // Store in state
        setToken(authToken);
        setUser(userData);
        setPatients(patientsData);
        
        // Store in AsyncStorage
        await AsyncStorage.setItem('auth_token', authToken);
        await AsyncStorage.setItem('user', JSON.stringify(userData));
        await AsyncStorage.setItem('patients', JSON.stringify(patientsData));
        
        // Auto-select patient if only one exists
        if (patientsData.length === 1) {
          setSelectedPatient(patientsData[0]);
          await AsyncStorage.setItem('selected_patient', JSON.stringify(patientsData[0]));
        }
        
        return true;
      }
      
      return false;
    } catch (error) {
      console.error('Login error:', error);
      return false;
    } finally {
      setIsLoading(false);
    }
  };

  const logout = async () => {
    try {
      // Clear state
      setUser(null);
      setPatients([]);
      setSelectedPatient(null);
      setToken(null);

      // Clear all stored data using AppResetManager
      await AppResetManager.clearAllData();
    } catch (error) {
      console.error('Logout error:', error);
    }
  };

  const selectPatient = async (patient: Patient) => {
    setSelectedPatient(patient);
    await AsyncStorage.setItem('selected_patient', JSON.stringify(patient));
  };

  const addPatient = async (patient: Patient) => {
    const updatedPatients = [...patients, patient];
    setPatients(updatedPatients);
    await AsyncStorage.setItem('patients', JSON.stringify(updatedPatients));
  };

  const value: AuthContextType = {
    user,
    patients,
    selectedPatient,
    token,
    isLoading,
    login,
    logout,
    selectPatient,
    addPatient,
    sendOTP,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = (): AuthContextType => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};
