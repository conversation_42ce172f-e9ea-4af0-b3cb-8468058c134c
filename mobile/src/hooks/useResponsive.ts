import { useState, useEffect } from 'react';
import { Dimensions, ScaledSize } from 'react-native';

interface ResponsiveInfo {
  width: number;
  height: number;
  isSmallScreen: boolean;
  isMediumScreen: boolean;
  isLargeScreen: boolean;
  isTablet: boolean;
  isPhone: boolean;
  orientation: 'portrait' | 'landscape';
  scale: number;
}

export const useResponsive = (): ResponsiveInfo => {
  const [dimensions, setDimensions] = useState(() => {
    const { width, height } = Dimensions.get('window');
    return { width, height };
  });

  useEffect(() => {
    const subscription = Dimensions.addEventListener('change', ({ window }: { window: ScaledSize }) => {
      setDimensions({ width: window.width, height: window.height });
    });

    return () => subscription?.remove();
  }, []);

  const { width, height } = dimensions;
  
  // Breakpoints
  const isSmallScreen = width < 768;
  const isMediumScreen = width >= 768 && width < 1024;
  const isLargeScreen = width >= 1024;
  
  // Device types
  const isTablet = width >= 768;
  const isPhone = width < 768;
  
  // Orientation
  const orientation = width > height ? 'landscape' : 'portrait';
  
  // Scale factor for responsive sizing
  const baseWidth = 375; // iPhone 6/7/8 width as base
  const scale = Math.min(width / baseWidth, 1.5); // Cap at 1.5x

  return {
    width,
    height,
    isSmallScreen,
    isMediumScreen,
    isLargeScreen,
    isTablet,
    isPhone,
    orientation,
    scale,
  };
};

// Responsive spacing utility
export const getResponsiveSpacing = (baseSpacing: number, scale: number): number => {
  return Math.round(baseSpacing * scale);
};

// Responsive font size utility
export const getResponsiveFontSize = (baseFontSize: number, scale: number): number => {
  return Math.round(baseFontSize * scale);
};

// Grid columns utility
export const getGridColumns = (width: number): number => {
  if (width < 768) return 1; // Mobile: 1 column
  if (width < 1024) return 2; // Tablet: 2 columns
  return 3; // Desktop: 3 columns
};

// Time slot grid utility
export const getTimeSlotColumns = (width: number): number => {
  if (width < 480) return 2; // Small mobile: 2 columns
  if (width < 768) return 3; // Large mobile: 3 columns
  if (width < 1024) return 4; // Tablet: 4 columns
  return 5; // Desktop: 5 columns
};
