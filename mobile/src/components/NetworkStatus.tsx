import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Alert } from 'react-native';
import ApiService from '../services/api';

interface NetworkStatusProps {
  onRetry?: () => void;
}

export const NetworkStatus: React.FC<NetworkStatusProps> = ({ onRetry }) => {
  const [isConnected, setIsConnected] = useState<boolean | null>(null);
  const [isChecking, setIsChecking] = useState(false);

  const checkConnection = async () => {
    setIsChecking(true);
    try {
      const connected = await ApiService.checkServerHealth();
      setIsConnected(connected);
      
      if (connected && onRetry) {
        onRetry();
      }
    } catch (error) {
      setIsConnected(false);
    } finally {
      setIsChecking(false);
    }
  };

  useEffect(() => {
    checkConnection();
  }, []);

  const handleRetry = () => {
    checkConnection();
  };

  const showConnectionHelp = () => {
    Alert.alert(
      'Connection Help',
      'Make sure:\n\n• You have an active internet connection\n• The backend server is running on localhost:3000\n• You are connected to the same network\n\nFor development: Ensure the backend server is started with "node backend/server-with-data.js"',
      [{ text: 'OK' }]
    );
  };

  if (isConnected === null) {
    return (
      <View style={styles.container}>
        <Text style={styles.checkingText}>Checking connection...</Text>
      </View>
    );
  }

  if (isConnected) {
    return (
      <View style={[styles.container, styles.connectedContainer]}>
        <Text style={styles.connectedText}>✅ Connected to server</Text>
      </View>
    );
  }

  return (
    <View style={[styles.container, styles.disconnectedContainer]}>
      <Text style={styles.disconnectedText}>❌ Cannot connect to server</Text>
      <View style={styles.buttonContainer}>
        <TouchableOpacity 
          style={styles.retryButton} 
          onPress={handleRetry}
          disabled={isChecking}
        >
          <Text style={styles.retryButtonText}>
            {isChecking ? 'Checking...' : 'Retry'}
          </Text>
        </TouchableOpacity>
        <TouchableOpacity style={styles.helpButton} onPress={showConnectionHelp}>
          <Text style={styles.helpButtonText}>Help</Text>
        </TouchableOpacity>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    padding: 12,
    margin: 16,
    borderRadius: 8,
    alignItems: 'center',
  },
  connectedContainer: {
    backgroundColor: '#dcfce7',
    borderColor: '#16a34a',
    borderWidth: 1,
  },
  disconnectedContainer: {
    backgroundColor: '#fef2f2',
    borderColor: '#dc2626',
    borderWidth: 1,
  },
  checkingText: {
    color: '#6b7280',
    fontSize: 14,
  },
  connectedText: {
    color: '#16a34a',
    fontSize: 14,
    fontWeight: '500',
  },
  disconnectedText: {
    color: '#dc2626',
    fontSize: 14,
    fontWeight: '500',
    marginBottom: 8,
  },
  buttonContainer: {
    flexDirection: 'row',
    gap: 8,
  },
  retryButton: {
    backgroundColor: '#2563eb',
    paddingHorizontal: 16,
    paddingVertical: 6,
    borderRadius: 4,
  },
  retryButtonText: {
    color: '#ffffff',
    fontSize: 12,
    fontWeight: '500',
  },
  helpButton: {
    backgroundColor: '#6b7280',
    paddingHorizontal: 16,
    paddingVertical: 6,
    borderRadius: 4,
  },
  helpButtonText: {
    color: '#ffffff',
    fontSize: 12,
    fontWeight: '500',
  },
});
