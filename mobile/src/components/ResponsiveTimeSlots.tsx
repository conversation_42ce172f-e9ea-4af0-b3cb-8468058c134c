import React from 'react';
import { View, Text, TouchableOpacity, StyleSheet } from 'react-native';
import { TimeSlot } from '../types';
import { useResponsive, getTimeSlotColumns } from '../hooks/useResponsive';

interface ResponsiveTimeSlotsProps {
  slots: TimeSlot[];
  selectedSlot: TimeSlot | null;
  onSlotSelect: (slot: TimeSlot) => void;
}

export const ResponsiveTimeSlots: React.FC<ResponsiveTimeSlotsProps> = ({
  slots,
  selectedSlot,
  onSlotSelect,
}) => {
  const { width, isTablet } = useResponsive();
  const columns = getTimeSlotColumns(width);

  const formatTime = (time: string) => {
    const [hours, minutes] = time.split(':');
    const hour = parseInt(hours);
    const ampm = hour >= 12 ? 'PM' : 'AM';
    const displayHour = hour === 0 ? 12 : hour > 12 ? hour - 12 : hour;
    return `${displayHour}:${minutes} ${ampm}`;
  };

  const renderTimeSlot = (slot: TimeSlot, index: number) => {
    const isSelected = selectedSlot?.id === slot.id;
    const slotWidth = (100 - (columns - 1) * 2) / columns; // 2% margin between slots

    return (
      <TouchableOpacity
        key={slot.id}
        style={[
          styles.timeSlot,
          isSelected && styles.selectedTimeSlot,
          {
            width: `${slotWidth}%`,
            marginRight: (index + 1) % columns === 0 ? 0 : '2%',
            marginBottom: isTablet ? 12 : 8,
          },
        ]}
        onPress={() => onSlotSelect(slot)}
      >
        <Text style={[
          styles.timeSlotText,
          isSelected && styles.selectedTimeSlotText,
          { fontSize: isTablet ? 16 : 14 }
        ]}>
          {formatTime(slot.start_time)}
        </Text>
        {isTablet && (
          <Text style={[
            styles.timeSlotSubtext,
            isSelected && styles.selectedTimeSlotSubtext,
          ]}>
            {slot.end_time ? `- ${formatTime(slot.end_time)}` : '30 min'}
          </Text>
        )}
      </TouchableOpacity>
    );
  };

  return (
    <View style={styles.container}>
      <View style={styles.slotsGrid}>
        {slots.map((slot, index) => renderTimeSlot(slot, index))}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  slotsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'flex-start',
  },
  timeSlot: {
    backgroundColor: '#ffffff',
    paddingVertical: 12,
    paddingHorizontal: 8,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#e5e7eb',
    alignItems: 'center',
    justifyContent: 'center',
    minHeight: 48,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 1,
  },
  selectedTimeSlot: {
    backgroundColor: '#2563eb',
    borderColor: '#2563eb',
    shadowColor: '#2563eb',
    shadowOpacity: 0.3,
    shadowRadius: 4,
    elevation: 3,
  },
  timeSlotText: {
    fontSize: 14,
    fontWeight: '600',
    color: '#374151',
    textAlign: 'center',
  },
  selectedTimeSlotText: {
    color: '#ffffff',
  },
  timeSlotSubtext: {
    fontSize: 12,
    color: '#6b7280',
    marginTop: 2,
    textAlign: 'center',
  },
  selectedTimeSlotSubtext: {
    color: '#dbeafe',
  },
});
