#!/usr/bin/env node

/**
 * Test script to verify app reset functionality and 7-day appointment limit
 */

const https = require('http');

const API_BASE_URL = 'http://localhost:3000';
const TEST_MOBILE = '+1234567890';
const TEST_OTP = '123456';

// Helper function to make HTTP requests
function makeRequest(options, data = null) {
  return new Promise((resolve, reject) => {
    const req = https.request(options, (res) => {
      let body = '';
      res.on('data', (chunk) => {
        body += chunk;
      });
      res.on('end', () => {
        try {
          const jsonBody = JSON.parse(body);
          resolve({ status: res.statusCode, data: jsonBody });
        } catch (error) {
          resolve({ status: res.statusCode, data: body });
        }
      });
    });

    req.on('error', (error) => {
      reject(error);
    });

    if (data) {
      req.write(JSON.stringify(data));
    }
    req.end();
  });
}

async function testHealthCheck() {
  console.log('🏥 Testing health check...');
  const options = {
    hostname: 'localhost',
    port: 3000,
    path: '/health',
    method: 'GET',
  };

  try {
    const response = await makeRequest(options);
    if (response.status === 200 && response.data.status === 'OK') {
      console.log('✅ Health check passed');
      return true;
    } else {
      console.log('❌ Health check failed:', response);
      return false;
    }
  } catch (error) {
    console.log('❌ Health check error:', error.message);
    return false;
  }
}

async function testSevenDayLimit() {
  console.log('📅 Testing 7-day appointment limit...');
  const options = {
    hostname: 'localhost',
    port: 3000,
    path: '/api/appointments/doctors/1/slots',
    method: 'GET',
  };

  try {
    const response = await makeRequest(options);
    if (response.status === 200 && response.data.success) {
      const slots = response.data.data;
      const uniqueDates = [...new Set(slots.map(slot => slot.date))];
      
      console.log(`✅ Found ${slots.length} total slots`);
      console.log(`✅ Covering ${uniqueDates.length} unique dates`);
      console.log(`✅ Date range: ${uniqueDates[0]} to ${uniqueDates[uniqueDates.length - 1]}`);
      
      // Calculate date difference
      const startDate = new Date(uniqueDates[0]);
      const endDate = new Date(uniqueDates[uniqueDates.length - 1]);
      const daysDiff = Math.ceil((endDate - startDate) / (1000 * 60 * 60 * 24)) + 1;
      
      if (daysDiff <= 7) {
        console.log(`✅ 7-day limit verified: ${daysDiff} days total`);
        return true;
      } else {
        console.log(`❌ 7-day limit exceeded: ${daysDiff} days`);
        return false;
      }
    } else {
      console.log('❌ Failed to get time slots:', response);
      return false;
    }
  } catch (error) {
    console.log('❌ Time slots error:', error.message);
    return false;
  }
}

async function testTimeSlotFormat() {
  console.log('⏰ Testing time slot format...');
  const options = {
    hostname: 'localhost',
    port: 3000,
    path: '/api/appointments/doctors/1/slots',
    method: 'GET',
  };

  try {
    const response = await makeRequest(options);
    if (response.status === 200 && response.data.success) {
      const slots = response.data.data;
      const firstSlot = slots[0];
      
      console.log(`✅ Sample slot: ${firstSlot.start_time} - ${firstSlot.end_time}`);
      
      // Check for invalid times like "09:60:00"
      const invalidTimes = slots.filter(slot => 
        slot.start_time.includes(':60:') || 
        slot.end_time.includes(':60:') ||
        slot.start_time.includes(':90:') || 
        slot.end_time.includes(':90:')
      );
      
      if (invalidTimes.length === 0) {
        console.log('✅ All time formats are valid');
        return true;
      } else {
        console.log(`❌ Found ${invalidTimes.length} invalid time formats`);
        console.log('❌ Examples:', invalidTimes.slice(0, 3));
        return false;
      }
    } else {
      console.log('❌ Failed to get time slots:', response);
      return false;
    }
  } catch (error) {
    console.log('❌ Time format test error:', error.message);
    return false;
  }
}

async function testCompleteBookingFlow() {
  console.log('🔄 Testing complete booking flow...');
  
  try {
    // 1. Send OTP
    console.log('  📱 Sending OTP...');
    const otpResponse = await makeRequest({
      hostname: 'localhost',
      port: 3000,
      path: '/api/auth/send-otp',
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
    }, { mobile_number: TEST_MOBILE });
    
    if (!otpResponse.data.success) {
      console.log('❌ OTP send failed');
      return false;
    }
    
    // 2. Verify OTP
    console.log('  🔐 Verifying OTP...');
    const authResponse = await makeRequest({
      hostname: 'localhost',
      port: 3000,
      path: '/api/auth/verify-otp',
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
    }, { mobile_number: TEST_MOBILE, otp_code: TEST_OTP });
    
    if (!authResponse.data.success) {
      console.log('❌ OTP verification failed');
      return false;
    }
    
    const patientId = authResponse.data.data.patients[0].id;
    
    // 3. Get time slots
    console.log('  ⏰ Getting time slots...');
    const slotsResponse = await makeRequest({
      hostname: 'localhost',
      port: 3000,
      path: '/api/appointments/doctors/1/slots',
      method: 'GET',
    });
    
    if (!slotsResponse.data.success || slotsResponse.data.data.length === 0) {
      console.log('❌ No time slots available');
      return false;
    }
    
    const timeSlot = slotsResponse.data.data[0];
    
    // 4. Book appointment
    console.log('  📅 Booking appointment...');
    const bookingResponse = await makeRequest({
      hostname: 'localhost',
      port: 3000,
      path: '/api/appointments/book',
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
    }, {
      patient_id: patientId,
      doctor_id: '1',
      time_slot_id: timeSlot.id,
      notes: 'Test booking for app reset verification'
    });
    
    if (bookingResponse.data.success) {
      console.log(`✅ Appointment booked successfully: ${bookingResponse.data.data.id}`);
      console.log(`✅ Date: ${timeSlot.date} at ${timeSlot.start_time}`);
      return true;
    } else {
      console.log('❌ Booking failed:', bookingResponse.data.message);
      return false;
    }
    
  } catch (error) {
    console.log('❌ Booking flow error:', error.message);
    return false;
  }
}

async function runTests() {
  console.log('🚀 Starting App Reset and 7-Day Limit Tests...\n');

  const tests = [
    { name: 'Health Check', fn: testHealthCheck },
    { name: '7-Day Appointment Limit', fn: testSevenDayLimit },
    { name: 'Time Slot Format Validation', fn: testTimeSlotFormat },
    { name: 'Complete Booking Flow', fn: testCompleteBookingFlow },
  ];

  let passed = 0;
  let failed = 0;

  for (const test of tests) {
    console.log(`\n🧪 Running: ${test.name}`);
    try {
      const result = await test.fn();
      if (result) {
        passed++;
        console.log(`✅ ${test.name} PASSED`);
      } else {
        failed++;
        console.log(`❌ ${test.name} FAILED`);
      }
    } catch (error) {
      failed++;
      console.log(`❌ ${test.name} ERROR:`, error.message);
    }
  }

  console.log('\n📊 Test Results Summary:');
  console.log(`✅ Passed: ${passed}`);
  console.log(`❌ Failed: ${failed}`);
  console.log(`📈 Success Rate: ${Math.round((passed / (passed + failed)) * 100)}%`);

  if (failed === 0) {
    console.log('\n🎉 All tests passed! App is working correctly.');
    console.log('\n📱 Frontend Instructions:');
    console.log('1. Open the mobile app at http://localhost:8081');
    console.log('2. Login with any mobile number using OTP: 123456');
    console.log('3. Go to Profile > Reset App (Dev) to test localStorage reset');
    console.log('4. Book appointments to verify 7-day limit');
  } else {
    console.log('\n⚠️  Some tests failed. Please check the issues above.');
    process.exit(1);
  }
}

// Run the tests
runTests().catch((error) => {
  console.error('❌ Test runner error:', error);
  process.exit(1);
});
