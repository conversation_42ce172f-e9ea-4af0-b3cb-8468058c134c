#!/usr/bin/env node

/**
 * Comprehensive test script for appointment booking functionality
 * Tests the complete end-to-end flow from authentication to booking
 */

const https = require('http');

const API_BASE_URL = 'http://localhost:3000';

// Test data
const TEST_MOBILE = '+1234567890';
const TEST_OTP = '123456';

// Helper function to make HTTP requests
function makeRequest(options, data = null) {
  return new Promise((resolve, reject) => {
    const req = https.request(options, (res) => {
      let body = '';
      res.on('data', (chunk) => {
        body += chunk;
      });
      res.on('end', () => {
        try {
          const jsonBody = JSON.parse(body);
          resolve({ status: res.statusCode, data: jsonBody });
        } catch (error) {
          resolve({ status: res.statusCode, data: body });
        }
      });
    });

    req.on('error', (error) => {
      reject(error);
    });

    if (data) {
      req.write(JSON.stringify(data));
    }
    req.end();
  });
}

// Test functions
async function testHealthCheck() {
  console.log('🏥 Testing health check...');
  const options = {
    hostname: 'localhost',
    port: 3000,
    path: '/health',
    method: 'GET',
  };

  try {
    const response = await makeRequest(options);
    if (response.status === 200 && response.data.status === 'OK') {
      console.log('✅ Health check passed');
      return true;
    } else {
      console.log('❌ Health check failed:', response);
      return false;
    }
  } catch (error) {
    console.log('❌ Health check error:', error.message);
    return false;
  }
}

async function testSendOTP() {
  console.log('📱 Testing send OTP...');
  const options = {
    hostname: 'localhost',
    port: 3000,
    path: '/api/auth/send-otp',
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
  };

  try {
    const response = await makeRequest(options, { mobile_number: TEST_MOBILE });
    if (response.status === 200 && response.data.success) {
      console.log('✅ Send OTP passed');
      return true;
    } else {
      console.log('❌ Send OTP failed:', response);
      return false;
    }
  } catch (error) {
    console.log('❌ Send OTP error:', error.message);
    return false;
  }
}

async function testVerifyOTP() {
  console.log('🔐 Testing verify OTP...');
  const options = {
    hostname: 'localhost',
    port: 3000,
    path: '/api/auth/verify-otp',
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
  };

  try {
    const response = await makeRequest(options, { 
      mobile_number: TEST_MOBILE, 
      otp_code: TEST_OTP 
    });
    if (response.status === 200 && response.data.success && response.data.data.patients) {
      console.log('✅ Verify OTP passed');
      console.log(`   Patient ID: ${response.data.data.patients[0].id}`);
      return response.data.data.patients[0].id;
    } else {
      console.log('❌ Verify OTP failed:', response);
      return null;
    }
  } catch (error) {
    console.log('❌ Verify OTP error:', error.message);
    return null;
  }
}

async function testGetSpecialties() {
  console.log('🏥 Testing get specialties...');
  const options = {
    hostname: 'localhost',
    port: 3000,
    path: '/api/appointments/specialties',
    method: 'GET',
  };

  try {
    const response = await makeRequest(options);
    if (response.status === 200 && response.data.success && response.data.data.length > 0) {
      console.log('✅ Get specialties passed');
      console.log(`   Found ${response.data.data.length} specialties:`, response.data.data);
      return response.data.data[0]; // Return first specialty
    } else {
      console.log('❌ Get specialties failed:', response);
      return null;
    }
  } catch (error) {
    console.log('❌ Get specialties error:', error.message);
    return null;
  }
}

async function testGetDoctors(specialty) {
  console.log(`👨‍⚕️ Testing get doctors for ${specialty}...`);
  const options = {
    hostname: 'localhost',
    port: 3000,
    path: `/api/appointments/doctors?specialty=${encodeURIComponent(specialty)}`,
    method: 'GET',
  };

  try {
    const response = await makeRequest(options);
    if (response.status === 200 && response.data.success && response.data.data.length > 0) {
      console.log('✅ Get doctors passed');
      console.log(`   Found doctor: ${response.data.data[0].name}`);
      return response.data.data[0]; // Return first doctor
    } else {
      console.log('❌ Get doctors failed:', response);
      return null;
    }
  } catch (error) {
    console.log('❌ Get doctors error:', error.message);
    return null;
  }
}

async function testGetTimeSlots(doctorId) {
  console.log(`⏰ Testing get time slots for doctor ${doctorId}...`);
  const options = {
    hostname: 'localhost',
    port: 3000,
    path: `/api/appointments/doctors/${doctorId}/slots`,
    method: 'GET',
  };

  try {
    const response = await makeRequest(options);
    if (response.status === 200 && response.data.success && response.data.data.length > 0) {
      console.log('✅ Get time slots passed');
      console.log(`   Found ${response.data.data.length} available slots`);
      console.log(`   First slot: ${response.data.data[0].date} at ${response.data.data[0].start_time}`);
      return response.data.data[0]; // Return first available slot
    } else {
      console.log('❌ Get time slots failed:', response);
      return null;
    }
  } catch (error) {
    console.log('❌ Get time slots error:', error.message);
    return null;
  }
}

async function testBookAppointment(patientId, doctorId, timeSlotId) {
  console.log('📅 Testing book appointment...');
  const options = {
    hostname: 'localhost',
    port: 3000,
    path: '/api/appointments/book',
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
  };

  const appointmentData = {
    patient_id: patientId,
    doctor_id: doctorId,
    time_slot_id: timeSlotId,
    notes: 'Test appointment booking from automated test'
  };

  try {
    const response = await makeRequest(options, appointmentData);
    if (response.status === 200 && response.data.success) {
      console.log('✅ Book appointment passed');
      console.log(`   Appointment ID: ${response.data.data.id}`);
      console.log(`   Status: ${response.data.data.status}`);
      console.log(`   Amount: $${response.data.data.amount}`);
      return response.data.data;
    } else {
      console.log('❌ Book appointment failed:', response);
      return null;
    }
  } catch (error) {
    console.log('❌ Book appointment error:', error.message);
    return null;
  }
}

async function testGetMedicalRecords(patientId) {
  console.log('📋 Testing get medical records...');
  const options = {
    hostname: 'localhost',
    port: 3000,
    path: `/api/patients/${patientId}/records`,
    method: 'GET',
  };

  try {
    const response = await makeRequest(options);
    if (response.status === 200 && response.data.success) {
      console.log('✅ Get medical records passed');
      console.log(`   Found ${response.data.data.length} medical records`);
      return response.data.data;
    } else {
      console.log('❌ Get medical records failed:', response);
      return null;
    }
  } catch (error) {
    console.log('❌ Get medical records error:', error.message);
    return null;
  }
}

// Main test function
async function runTests() {
  console.log('🚀 Starting comprehensive appointment booking tests...\n');

  // Test 1: Health check
  const healthOk = await testHealthCheck();
  if (!healthOk) {
    console.log('\n❌ Tests failed at health check. Make sure the backend server is running.');
    process.exit(1);
  }

  // Test 2: Send OTP
  const otpSent = await testSendOTP();
  if (!otpSent) {
    console.log('\n❌ Tests failed at send OTP.');
    process.exit(1);
  }

  // Test 3: Verify OTP and get patient
  const patientId = await testVerifyOTP();
  if (!patientId) {
    console.log('\n❌ Tests failed at verify OTP.');
    process.exit(1);
  }

  // Test 4: Get specialties
  const specialty = await testGetSpecialties();
  if (!specialty) {
    console.log('\n❌ Tests failed at get specialties.');
    process.exit(1);
  }

  // Test 5: Get doctors
  const doctor = await testGetDoctors(specialty);
  if (!doctor) {
    console.log('\n❌ Tests failed at get doctors.');
    process.exit(1);
  }

  // Test 6: Get time slots
  const timeSlot = await testGetTimeSlots(doctor.id);
  if (!timeSlot) {
    console.log('\n❌ Tests failed at get time slots.');
    process.exit(1);
  }

  // Test 7: Book appointment
  const appointment = await testBookAppointment(patientId, doctor.id, timeSlot.id);
  if (!appointment) {
    console.log('\n❌ Tests failed at book appointment.');
    process.exit(1);
  }

  // Test 8: Get medical records
  const records = await testGetMedicalRecords(patientId);
  if (records === null) {
    console.log('\n❌ Tests failed at get medical records.');
    process.exit(1);
  }

  console.log('\n🎉 All tests passed! Appointment booking functionality is working correctly.');
  console.log('\n📊 Test Summary:');
  console.log('✅ Health check');
  console.log('✅ Send OTP');
  console.log('✅ Verify OTP');
  console.log('✅ Get specialties');
  console.log('✅ Get doctors');
  console.log('✅ Get time slots');
  console.log('✅ Book appointment');
  console.log('✅ Get medical records');
}

// Run the tests
runTests().catch((error) => {
  console.error('❌ Test runner error:', error);
  process.exit(1);
});
