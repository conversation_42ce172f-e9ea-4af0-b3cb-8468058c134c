const express = require('express');
const cors = require('cors');

const app = express();
const PORT = 3000;

// Middleware
app.use(cors());
app.use(express.json());

// In-memory data store for demo
let users = [];
let patients = [];
let doctors = [
  {
    id: '1',
    name: '<PERSON>',
    specialty: 'Cardiology',
    qualification: 'MD, FACC - Harvard Medical School',
    experience_years: 15
  },
  {
    id: '2',
    name: '<PERSON>',
    specialty: 'Neurology',
    qualification: 'MD, PhD - Johns Hopkins University',
    experience_years: 12
  },
  {
    id: '3',
    name: '<PERSON>',
    specialty: 'Pediatrics',
    qualification: 'MD - Stanford University',
    experience_years: 8
  },
  {
    id: '4',
    name: '<PERSON>',
    specialty: 'Orthopedics',
    qualification: 'MD, MS - Mayo Clinic',
    experience_years: 20
  }
];

let timeSlots = [];
let appointments = [];
let medicalRecords = [];
let otpSessions = [];

// Generate time slots for next 30 days
function generateTimeSlots() {
  const slots = [];
  const today = new Date();
  
  for (let i = 1; i <= 30; i++) {
    const date = new Date(today);
    date.setDate(today.getDate() + i);
    
    // Skip weekends
    if (date.getDay() === 0 || date.getDay() === 6) continue;
    
    doctors.forEach(doctor => {
      // Morning slots: 9:00 AM to 12:00 PM
      for (let hour = 9; hour < 12; hour++) {
        for (let minute = 0; minute < 60; minute += 30) {
          const endMinute = minute + 30;
          const endHour = endMinute >= 60 ? hour + 1 : hour;
          const adjustedEndMinute = endMinute >= 60 ? 0 : endMinute;

          slots.push({
            id: `${doctor.id}-${date.toISOString().split('T')[0]}-${hour}-${minute}`,
            doctor_id: doctor.id,
            date: date.toISOString().split('T')[0],
            start_time: `${hour.toString().padStart(2, '0')}:${minute.toString().padStart(2, '0')}:00`,
            end_time: `${endHour.toString().padStart(2, '0')}:${adjustedEndMinute.toString().padStart(2, '0')}:00`,
            is_available: true
          });
        }
      }
      
      // Afternoon slots: 2:00 PM to 6:00 PM
      for (let hour = 14; hour < 18; hour++) {
        for (let minute = 0; minute < 60; minute += 30) {
          slots.push({
            id: `${doctor.id}-${date.toISOString().split('T')[0]}-${hour}-${minute}`,
            doctor_id: doctor.id,
            date: date.toISOString().split('T')[0],
            start_time: `${hour.toString().padStart(2, '0')}:${minute.toString().padStart(2, '0')}:00`,
            end_time: `${hour.toString().padStart(2, '0')}:${(minute + 30).toString().padStart(2, '0')}:00`,
            is_available: true
          });
        }
      }
    });
  }
  
  return slots;
}

timeSlots = generateTimeSlots();

// Helper function to generate ID
function generateId() {
  return Math.random().toString(36).substr(2, 9);
}

// Health check endpoint
app.get('/health', (req, res) => {
  res.status(200).json({
    status: 'OK',
    message: 'Patient Portal API is running',
    timestamp: new Date().toISOString()
  });
});

// Auth endpoints
app.post('/api/auth/send-otp', (req, res) => {
  const { mobile_number } = req.body;
  
  if (!mobile_number) {
    return res.status(400).json({
      success: false,
      message: 'Mobile number is required'
    });
  }
  
  // Remove existing OTP sessions for this mobile
  otpSessions = otpSessions.filter(session => session.mobile_number !== mobile_number);
  
  // Create new OTP session
  const otpSession = {
    id: generateId(),
    mobile_number,
    otp_code: '123456', // Fixed OTP for testing
    expires_at: new Date(Date.now() + 5 * 60 * 1000), // 5 minutes
    is_verified: false,
    created_at: new Date()
  };
  
  otpSessions.push(otpSession);
  
  console.log(`OTP for ${mobile_number}: 123456`);
  
  res.json({
    success: true,
    message: 'OTP sent successfully'
  });
});

app.post('/api/auth/verify-otp', (req, res) => {
  const { mobile_number, otp_code } = req.body;
  
  if (!mobile_number || !otp_code) {
    return res.status(400).json({
      success: false,
      message: 'Mobile number and OTP are required'
    });
  }
  
  // Find valid OTP session
  const otpSession = otpSessions.find(session => 
    session.mobile_number === mobile_number && 
    session.otp_code === otp_code && 
    session.expires_at > new Date() && 
    !session.is_verified
  );
  
  if (!otpSession) {
    return res.status(400).json({
      success: false,
      message: 'Invalid or expired OTP'
    });
  }
  
  // Mark OTP as verified
  otpSession.is_verified = true;
  
  // Find or create user
  let user = users.find(u => u.mobile_number === mobile_number);
  if (!user) {
    user = {
      id: generateId(),
      mobile_number,
      created_at: new Date(),
      updated_at: new Date()
    };
    users.push(user);
    
    // Create sample patient for new user
    const samplePatient = {
      id: generateId(),
      user_id: user.id,
      name: 'John Doe',
      age: 35,
      gender: 'male',
      date_of_birth: '1988-05-15',
      contact_info: {
        email: '<EMAIL>',
        mobile: mobile_number,
        emergency_contact: '+**********'
      },
      address: {
        street: '123 Main St',
        city: 'New York',
        state: 'NY',
        postal_code: '10001',
        country: 'United States'
      },
      created_at: new Date(),
      updated_at: new Date()
    };
    patients.push(samplePatient);
    
    // Add sample medical records
    const sampleRecords = [
      {
        id: generateId(),
        patient_id: samplePatient.id,
        type: 'pathology',
        title: 'Blood Test Results',
        description: 'Complete Blood Count and Lipid Panel',
        data: {
          hemoglobin: '14.2 g/dL',
          cholesterol: '180 mg/dL',
          glucose: '95 mg/dL',
          white_blood_cells: '7500/μL'
        },
        date: new Date(Date.now() - 15 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
        doctor_id: doctors[0].id,
        doctor_name: doctors[0].name,
        created_at: new Date(),
        updated_at: new Date()
      },
      {
        id: generateId(),
        patient_id: samplePatient.id,
        type: 'vitals',
        title: 'Routine Checkup Vitals',
        description: 'Regular health monitoring',
        data: {
          blood_pressure: '120/80 mmHg',
          heart_rate: '72 bpm',
          temperature: '98.6°F',
          weight: '175 lbs',
          height: '5\'10"'
        },
        date: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
        doctor_id: doctors[0].id,
        doctor_name: doctors[0].name,
        created_at: new Date(),
        updated_at: new Date()
      },
      {
        id: generateId(),
        patient_id: samplePatient.id,
        type: 'medication',
        title: 'Current Medications',
        description: 'Active prescriptions',
        data: {
          medications: [
            { name: 'Lisinopril', dosage: '10mg', frequency: 'Once daily' },
            { name: 'Metformin', dosage: '500mg', frequency: 'Twice daily' }
          ]
        },
        date: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
        doctor_id: doctors[0].id,
        doctor_name: doctors[0].name,
        created_at: new Date(),
        updated_at: new Date()
      }
    ];
    medicalRecords.push(...sampleRecords);
  }
  
  // Get user's patients
  const userPatients = patients.filter(p => p.user_id === user.id);
  
  res.json({
    success: true,
    message: 'OTP verified successfully',
    data: {
      token: 'mock-jwt-token-' + user.id,
      user,
      patients: userPatients
    }
  });
});

// Patient endpoints
app.get('/api/patients/by-mobile', (req, res) => {
  // Mock authentication - in real app, verify JWT token
  const userPatients = patients.filter(p => p.user_id === users[0]?.id);
  
  res.json({
    success: true,
    message: 'Patients retrieved successfully',
    data: userPatients
  });
});

app.post('/api/patients/register', (req, res) => {
  const patientData = req.body;
  
  const newPatient = {
    id: generateId(),
    user_id: users[0]?.id || generateId(),
    name: patientData.name,
    age: patientData.age,
    gender: patientData.gender,
    date_of_birth: patientData.date_of_birth,
    contact_info: patientData.contact_info,
    address: patientData.address,
    created_at: new Date(),
    updated_at: new Date()
  };
  
  patients.push(newPatient);
  
  res.json({
    success: true,
    message: 'Patient registered successfully',
    data: newPatient
  });
});

app.get('/api/patients/:patientId/records', (req, res) => {
  const { patientId } = req.params;
  const patientRecords = medicalRecords.filter(r => r.patient_id === patientId);
  
  res.json({
    success: true,
    message: 'Medical records retrieved successfully',
    data: patientRecords
  });
});

// Appointment endpoints
app.get('/api/appointments/specialties', (req, res) => {
  const specialties = [...new Set(doctors.map(d => d.specialty))];
  
  res.json({
    success: true,
    message: 'Specialties retrieved successfully',
    data: specialties
  });
});

app.get('/api/appointments/doctors', (req, res) => {
  const { specialty } = req.query;
  let filteredDoctors = doctors;
  
  if (specialty) {
    filteredDoctors = doctors.filter(d => d.specialty.toLowerCase() === specialty.toLowerCase());
  }
  
  res.json({
    success: true,
    message: 'Doctors retrieved successfully',
    data: filteredDoctors
  });
});

app.get('/api/appointments/doctors/:doctorId/slots', (req, res) => {
  const { doctorId } = req.params;
  const { date } = req.query;
  
  let availableSlots = timeSlots.filter(slot => 
    slot.doctor_id === doctorId && 
    slot.is_available &&
    new Date(slot.date) >= new Date()
  );
  
  if (date) {
    availableSlots = availableSlots.filter(slot => slot.date === date);
  }
  
  res.json({
    success: true,
    message: 'Available slots retrieved successfully',
    data: availableSlots
  });
});

app.post('/api/appointments/book', (req, res) => {
  const { patient_id, doctor_id, time_slot_id, notes } = req.body;
  
  // Find the time slot
  const timeSlot = timeSlots.find(slot => slot.id === time_slot_id);
  if (!timeSlot || !timeSlot.is_available) {
    return res.status(400).json({
      success: false,
      message: 'Time slot is no longer available'
    });
  }
  
  // Mark slot as unavailable
  timeSlot.is_available = false;
  
  // Create appointment
  const appointment = {
    id: generateId(),
    patient_id,
    doctor_id,
    time_slot_id,
    status: 'scheduled',
    payment_status: 'paid',
    amount: 500,
    notes: notes || null,
    created_at: new Date(),
    updated_at: new Date()
  };
  
  appointments.push(appointment);
  
  res.json({
    success: true,
    message: 'Appointment booked successfully',
    data: appointment
  });
});

app.get('/api/appointments/patient/:patientId', (req, res) => {
  const { patientId } = req.params;
  
  const patientAppointments = appointments
    .filter(a => a.patient_id === patientId)
    .map(appointment => {
      const doctor = doctors.find(d => d.id === appointment.doctor_id);
      const timeSlot = timeSlots.find(s => s.id === appointment.time_slot_id);
      
      return {
        ...appointment,
        doctor_name: doctor?.name,
        specialty: doctor?.specialty,
        date: timeSlot?.date,
        start_time: timeSlot?.start_time,
        end_time: timeSlot?.end_time
      };
    });
  
  res.json({
    success: true,
    message: 'Appointments retrieved successfully',
    data: patientAppointments
  });
});

// Start server
app.listen(PORT, () => {
  console.log(`🚀 Server is running on port ${PORT}`);
  console.log(`📱 Patient Portal API is ready!`);
  console.log(`🏥 Health check: http://localhost:${PORT}/health`);
  console.log(`🔑 Test OTP: 123456 for any mobile number`);
});
